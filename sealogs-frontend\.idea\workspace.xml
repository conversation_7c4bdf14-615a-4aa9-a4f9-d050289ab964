<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="747aec76-0114-41f2-9c2f-e591ff5c1182" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/src/app/ui/logbook/forms/bar-crossing.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/ui/logbook/forms/bar-crossing.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/app/ui/logbook/forms/tasking.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/app/ui/logbook/forms/tasking.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/ui/risk-analysis/risk-analysis-sheet.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/ui/risk-analysis/risk-analysis-sheet.tsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="PhpWorkspaceProjectConfiguration" interpreter_name="PHP" />
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 3
}]]></component>
  <component name="ProjectId" id="2zMGPmtVZJQpYMrIwFexqvEbBz8" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "brent-develop",
    "last_opened_file_path": "C:/Users/<USER>/Music/SeaLogsV2/sealogs-frontend",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "pnpm",
    "ts.external.directory.path": "C:\\Users\\<USER>\\Music\\SeaLogsV2\\sealogs-frontend\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-e768b9ed790e-JavaScript-PS-243.21565.202" />
        <option value="bundled-php-predefined-a98d8de5180a-5fbe0c30323e-com.jetbrains.php.sharedIndexes-PS-243.21565.202" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="747aec76-0114-41f2-9c2f-e591ff5c1182" name="Changes" comment="" />
      <created>1751533507391</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751533507391</updated>
      <workItem from="1751533508808" duration="126000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>