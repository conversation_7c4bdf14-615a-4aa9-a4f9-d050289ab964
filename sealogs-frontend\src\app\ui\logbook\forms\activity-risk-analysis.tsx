'use client'
import React, { useEffect, useState } from 'react'
// React and state management imports
import {
    UpdateDangerousGoodsChecklist,
    CreateMitigationStrategy,
    UpdateMitigationStrategy,
    CreateRiskFactor,
    UpdateRiskFactor,
} from '@/app/lib/graphQL/mutation'
import {
    GetOneDangerousGoodsChecklist,
    GetRiskFactors,
    CrewMembers_LogBookEntrySection,
} from '@/app/lib/graphQL/query'
import { useLazyQuery, useMutation } from '@apollo/client'
import { useToast } from '@/hooks/use-toast'
import { useSearchParams } from 'next/navigation'
import { getLogBookEntryByID } from '@/app/lib/actions'
import LogBookEntryModel from '@/app/offline/models/logBookEntry'
import DangerousGoodsChecklistModel from '@/app/offline/models/dangerousGoodsChecklist'
import RiskFactorModel from '@/app/offline/models/riskFactor'
import CrewMembers_LogBookEntrySectionModel from '@/app/offline/models/crewMembers_LogBookEntrySection'
import MitigationStrategyModel from '@/app/offline/models/mitigationStrategy'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import { P } from '@/components/ui/typography'
import { AlertDialogNew } from '@/components/ui'
import {
    RiskAnalysisSheet,
    RiskDialog,
    StrategyDialog,
} from '@/components/ui/risk-analysis'
// Define risk impacts used in the impact Select
const riskImpacts = [
    { value: 'Low', label: 'Low impact' },
    { value: 'Medium', label: 'Medium impact' },
    { value: 'High', label: 'High impact' },
    { value: 'Severe', label: 'Severe impact' },
]

export default function ActivityRiskAnalysis({
    onSidebarClose,
    logBookConfig,
    currentTrip,
    crewMembers = false,
    open,
    onOpenChange,
    editDGR = false,
    offline = false,
    setAllChecked,
}: {
    onSidebarClose?: any
    logBookConfig?: any
    currentTrip?: any
    crewMembers?: any
    open: boolean
    onOpenChange: (open: boolean) => void
    editDGR?: boolean
    offline?: boolean
    setAllChecked: any
}) {
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const logentryID = searchParams.get('logentryID') ?? 0
    const [riskAnalysis, setRiskAnalysis] = useState<any>({
        riskFactors: { nodes: [] },
    })
    const [riskBuffer, setRiskBuffer] = useState<any>(false)
    const [openRiskDialog, setOpenRiskDialog] = useState(false)
    const [currentRisk, setCurrentRisk] = useState<any>(false)
    const [content, setContent] = useState<any>('')
    const [allRisks, setAllRisks] = useState<any>([])
    const [allRiskFactors, setAllRiskFactors] = useState<any>([])
    const [riskValue, setRiskValue] = useState<any>(null)
    const [updateStrategy, setUpdateStrategy] = useState(false)
    const [strategyEditor, setstrategyEditor] = useState<any>(false)
    const [openRecommendedstrategy, setOpenRecommendedstrategy] =
        useState(false)
    const [recommendedStratagies, setRecommendedStratagies] =
        useState<any>(false)
    const [currentStrategies, setCurrentStrategies] = useState<any>([])
    const [recommendedstrategy, setRecommendedstrategy] = useState<any>(false)
    const [riskToDelete, setRiskToDelete] = useState<any>(false)
    const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false)
    const [logbook, setLogbook] = useState<any>(false)
    const [members, setMembers] = useState<any>([])
    const { toast } = useToast()

    const logBookEntryModel = new LogBookEntryModel()
    const dangerousGoodsChecklistModel = new DangerousGoodsChecklistModel()
    const riskFactorModel = new RiskFactorModel()
    const crewMemberModel = new CrewMembers_LogBookEntrySectionModel()
    const mitigationStrategyModel = new MitigationStrategyModel()

    const [getSectionCrewMembers_LogBookEntrySection] = useLazyQuery(
        CrewMembers_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                let data = response.readCrewMembers_LogBookEntrySections.nodes
                const crew = data
                    .map((member: any) => {
                        return {
                            label: `${member.crewMember.firstName ?? ''} ${member.crewMember.surname ?? ''}`,
                            value: member.crewMember.id,
                        }
                    })
                    .filter((member: any) => member.value != logbook.master.id)
                setMembers([...members, ...crew])
            },
            onError: (error: any) => {
                console.error('CrewMembers_LogBookEntrySection error', error)
            },
        },
    )

    const handleSetLogbook = async (logbook: any) => {
        setLogbook(logbook)
        const master = {
            label: `${logbook.master.firstName ?? ''} ${logbook.master.surname ?? ''}`,
            value: logbook.master.id,
        }
        setMembers([master])
        const sections = logbook.logBookEntrySections.nodes.filter(
            (node: any) =>
                node.className === 'SeaLogs\\CrewMembers_LogBookEntrySection',
        )
        if (sections) {
            const sectionIDs = sections.map((section: any) => section.id)
            if (sectionIDs?.length > 0) {
                if (offline) {
                    const data = await crewMemberModel.getByIds(sectionIDs)
                    const crew = data
                        .map((member: any) => {
                            return {
                                label: `${member.crewMember.firstName ?? ''} ${member.crewMember.surname ?? ''}`,
                                value: member.crewMember.id,
                            }
                        })
                        .filter(
                            (member: any) => member.value != logbook.master.id,
                        )
                    setMembers([...members, ...crew])
                } else {
                    getSectionCrewMembers_LogBookEntrySection({
                        variables: {
                            filter: { id: { in: sectionIDs } },
                        },
                    })
                }
            }
        }
    }

    if (+logentryID > 0 && !offline) {
        getLogBookEntryByID(+logentryID, handleSetLogbook)
    }
    const offlineUseEffect = async () => {
        const logbook = await logBookEntryModel.getById(+logentryID)
        handleSetLogbook(logbook)
    }
    useEffect(() => {
        if (offline) {
            offlineUseEffect()
        }
    }, [offline])
    useEffect(() => {
        if (crewMembers) {
            setMembers(crewMembers)
        }
    }, [crewMembers])

    // Initialize risk factors
    const offlineMount = async () => {
        try {
            const data = await riskFactorModel.getByFieldID(
                'type',
                'DangerousGoods',
            )

            if (!data || !Array.isArray(data) || data.length === 0) {
                setAllRisks([])
                setAllRiskFactors([])
                return
            }

            const titles = data.map((risk: any) => risk.title).filter(Boolean)
            const uniqueTitles = Array.from(new Set(titles))
            const risks = uniqueTitles.map((risk: any) => ({
                label: risk,
                value: risk,
            }))

            setAllRisks(risks)
            setAllRiskFactors(data)
        } catch (error) {
            console.error('Error in offlineMount:', error)
            setAllRisks([])
            setAllRiskFactors([])
        }
    }

    useEffect(() => {
        if (offline) {
            offlineMount()
        } else {
            // Fetch risk factors when component mounts
            getRiskFactors({
                variables: {
                    filter: { type: { eq: 'DangerousGoods' } },
                },
            })
        }
    }, [])

    // Initialize risk analysis data
    const offlineOpenRiskAnalysis = async () => {
        const data = await dangerousGoodsChecklistModel.getById(
            currentTrip.dangerousGoodsChecklist.id,
        )
        setRiskAnalysis(data || { riskFactors: { nodes: [] } })
        if (!riskBuffer) {
            setRiskBuffer({
                vesselSecuredToWharf:
                    data?.vesselSecuredToWharf == true ? 'on' : 'off',
                bravoFlagRaised: data?.bravoFlagRaised == true ? 'on' : 'off',
                twoCrewLoadingVessel:
                    data?.twoCrewLoadingVessel == true ? 'on' : 'off',
                fireHosesRiggedAndReady:
                    data?.fireHosesRiggedAndReady == true ? 'on' : 'off',
                noSmokingSignagePosted:
                    data?.noSmokingSignagePosted == true ? 'on' : 'off',
                spillKitAvailable:
                    data?.spillKitAvailable == true ? 'on' : 'off',
                fireExtinguishersAvailable:
                    data?.fireExtinguishersAvailable == true ? 'on' : 'off',
                dgDeclarationReceived:
                    data?.dgDeclarationReceived == true ? 'on' : 'off',
                loadPlanReceived: data?.loadPlanReceived == true ? 'on' : 'off',
                msdsAvailable: data?.msdsAvailable == true ? 'on' : 'off',
                anyVehiclesSecureToVehicleDeck:
                    data?.anyVehiclesSecureToVehicleDeck == true ? 'on' : 'off',
                safetyAnnouncement:
                    data?.safetyAnnouncement == true ? 'on' : 'off',
                vehicleStationaryAndSecure:
                    data?.vehicleStationaryAndSecure == true ? 'on' : 'off',
                memberID: data?.member?.id,
            })
        }
    }

    useEffect(() => {
        if (open) {
            if (offline) {
                offlineOpenRiskAnalysis()
            } else {
                getRiskAnalysis({
                    variables: {
                        id: currentTrip.dangerousGoodsChecklist.id,
                    },
                })
            }
        }
    }, [open])

    const [updateDangerousGoodsChecklist] = useMutation(
        UpdateDangerousGoodsChecklist,
        {
            onCompleted: (data) => {},
            onError: (error) => {
                console.error('onError', error)
            },
        },
    )

    const handleDgrFieldChange = (field: string) => async (check: boolean) => {
        if (!editDGR) {
            toast({
                title: 'Permission Denied',
                description: 'You do not have permission to edit this section',
                variant: 'destructive',
            })
            return
        }
        setRiskBuffer({
            ...riskBuffer,
            [field]: check ? 'on' : 'off',
        })
        if (+currentTrip.dangerousGoodsChecklist.id > 0) {
            if (offline) {
                await dangerousGoodsChecklistModel.save({
                    id: currentTrip.dangerousGoodsChecklist.id,
                    [field]: check ? true : false,
                })
            } else {
                updateDangerousGoodsChecklist({
                    variables: {
                        input: {
                            id: currentTrip.dangerousGoodsChecklist.id,
                            [field]: check ? true : false,
                        },
                    },
                })
            }
        }
    }

    // Helper function to determine if a field is "checked"
    const checkFieldVal = (field: string) => {
        if (riskBuffer) {
            return riskBuffer[field] === 'on'
        }
        return riskAnalysis?.[field]
    }

    // Define your fields with a proper "checked" key
    const checkFields = [
        {
            name: 'VesselSecuredToWharf',
            label: 'Vessel secured to wharf',
            value: 'vesselSecuredToWharf',
            checked: checkFieldVal('vesselSecuredToWharf'),
            handleChange: handleDgrFieldChange('vesselSecuredToWharf'),
            description: (
                <small>
                    <div>Conduct SAP prior to approaching the vessel.</div>
                    <div>
                        Check for fittings on the vessel that could damage the
                        CRV when coming alongside.
                    </div>
                </small>
            ),
        },
        {
            name: 'BravoFlagRaised',
            label: 'Bravo flag raised',
            value: 'bravoFlagRaised',
            checked: riskBuffer?.bravoFlagRaised
                ? riskBuffer.bravoFlagRaised === 'on'
                : riskAnalysis?.bravoFlagRaised,
            handleChange: handleDgrFieldChange('bravoFlagRaised'),
            description: (
                <small>
                    <div>
                        Ascertain the nature of the problem, any damage, or
                        taking on water.
                    </div>
                    <div>
                        Does a crew member need to go on board the other vessel
                        to assist?
                    </div>
                </small>
            ),
        },
        {
            name: 'TwoCrewLoadingVessel',
            label: 'Two crew loading vessel',
            value: 'twoCrewLoadingVessel',
            checked: riskBuffer?.twoCrewLoadingVessel
                ? riskBuffer.twoCrewLoadingVessel === 'on'
                : riskAnalysis?.twoCrewLoadingVessel,
            handleChange: handleDgrFieldChange('twoCrewLoadingVessel'),
            description: (
                <small>
                    <div>
                        Check how many people are aboard, ensure everyone is
                        accounted for.
                    </div>
                    <div>
                        Check for injuries or medical assistance required.
                    </div>
                </small>
            ),
        },
        {
            name: 'FireHosesRiggedAndReady',
            label: 'Fire hoses rigged and ready',
            value: 'fireHosesRiggedAndReady',
            checked: riskBuffer?.fireHosesRiggedAndReady
                ? riskBuffer.fireHosesRiggedAndReady === 'on'
                : riskAnalysis?.fireHosesRiggedAndReady,
            handleChange: handleDgrFieldChange('fireHosesRiggedAndReady'),
        },
        {
            name: 'NoSmokingSignagePosted',
            label: 'No smoking signage posted',
            value: 'noSmokingSignagePosted',
            checked: riskBuffer?.noSmokingSignagePosted
                ? riskBuffer.noSmokingSignagePosted === 'on'
                : riskAnalysis?.noSmokingSignagePosted,
            handleChange: handleDgrFieldChange('noSmokingSignagePosted'),
            description: (
                <small>
                    <div>Request that everyone wears a lifejacket.</div>
                </small>
            ),
        },
        {
            name: 'SpillKitAvailable',
            label: 'Spill kit available',
            value: 'spillKitAvailable',
            checked: riskBuffer?.spillKitAvailable
                ? riskBuffer.spillKitAvailable === 'on'
                : riskAnalysis?.spillKitAvailable,
            handleChange: handleDgrFieldChange('spillKitAvailable'),
            description: (
                <small>
                    <div>
                        Ensure that communications have been established and
                        checked prior to beginning the tow (VHF, hand signals,
                        and/or light signals).
                    </div>
                    <div>
                        Ensure there is agreement on where to tow the vessel to.
                    </div>
                </small>
            ),
        },
        {
            name: 'FireExtinguishersAvailable',
            label: 'Fire extinguishers available',
            value: 'fireExtinguishersAvailable',
            checked: riskBuffer?.fireExtinguishersAvailable
                ? riskBuffer.fireExtinguishersAvailable === 'on'
                : riskAnalysis?.fireExtinguishersAvailable,
            handleChange: handleDgrFieldChange('fireExtinguishersAvailable'),
            description: (
                <small>
                    <div>Towline securely attached</div>
                    <div>Ensure everything on board is stowed and secure.</div>
                    <div>
                        Confirm waterline length/cruising speed of the vessel
                        (safe tow speed).
                    </div>
                    <div>Confirm attachment points for the towline.</div>
                    <div>Confirm that the towline is securely attached.</div>
                    <div>
                        Ensure that no one on the other vessel is in close
                        proximity to the towline before commencing the tow.
                    </div>
                    <div>
                        Turn on CRV towing lights and other vessel’s navigation
                        lights.
                    </div>
                    <div>
                        Post towline lookout with responsibility for quick
                        release of the tow (must carry or have a knife handy).
                    </div>
                </small>
            ),
        },
        {
            name: 'DGDeclarationReceived',
            label: 'DG declaration received',
            value: 'dgDeclarationReceived',
            checked: riskBuffer?.dgDeclarationReceived
                ? riskBuffer.dgDeclarationReceived === 'on'
                : riskAnalysis?.dgDeclarationReceived,
            handleChange: handleDgrFieldChange('dgDeclarationReceived'),
            description: (
                <small>
                    <div>Conduct SAP prior to approaching the vessel.</div>
                    <div>
                        Check for fittings on the vessel that could damage the
                        CRV when coming alongside.
                    </div>
                </small>
            ),
        },
        {
            name: 'LoadPlanReceived',
            label: 'Load plan received',
            value: 'loadPlanReceived',
            checked: riskBuffer?.loadPlanReceived
                ? riskBuffer.loadPlanReceived === 'on'
                : riskAnalysis?.loadPlanReceived,
            handleChange: handleDgrFieldChange('loadPlanReceived'),
            description: (
                <small>
                    <div>Conduct SAP prior to approaching the vessel.</div>
                    <div>
                        Check for fittings on the vessel that could damage the
                        CRV when coming alongside.
                    </div>
                </small>
            ),
        },
        {
            name: 'MSDSAvailable',
            label: 'MSDS available for all dangerous goods carried',
            value: 'msdsAvailable',
            checked: riskBuffer?.msdsAvailable
                ? riskBuffer.msdsAvailable === 'on'
                : riskAnalysis?.msdsAvailable,
            handleChange: handleDgrFieldChange('msdsAvailable'),
            description: (
                <small>
                    <div>Conduct SAP prior to approaching the vessel.</div>
                    <div>
                        Check for fittings on the vessel that could damage the
                        CRV when coming alongside.
                    </div>
                </small>
            ),
        },
        {
            name: 'AnyVehiclesSecureToVehicleDeck',
            label: 'Any vehicles secure to vehicle deck',
            value: 'anyVehiclesSecureToVehicleDeck',
            checked: riskBuffer?.anyVehiclesSecureToVehicleDeck
                ? riskBuffer.anyVehiclesSecureToVehicleDeck === 'on'
                : riskAnalysis?.anyVehiclesSecureToVehicleDeck,
            handleChange: handleDgrFieldChange(
                'anyVehiclesSecureToVehicleDeck',
            ),
            description: (
                <small>
                    <div>Conduct SAP prior to approaching the vessel.</div>
                    <div>
                        Check for fittings on the vessel that could damage the
                        CRV when coming alongside.
                    </div>
                </small>
            ),
        },
        {
            name: 'SafetyAnnouncement',
            label: 'Safety announcement includes reference to dangerous goods & no smoking',
            value: 'safetyAnnouncement',
            checked: riskBuffer?.safetyAnnouncement
                ? riskBuffer.safetyAnnouncement === 'on'
                : riskAnalysis?.safetyAnnouncement,
            handleChange: handleDgrFieldChange('safetyAnnouncement'),
            description: (
                <small>
                    <div>Conduct SAP prior to approaching the vessel.</div>
                    <div>
                        Check for fittings on the vessel that could damage the
                        CRV when coming alongside.
                    </div>
                </small>
            ),
        },
        {
            name: 'VehicleStationaryAndSecure',
            label: 'Vehicle stationary and secure prior to vehicle departing vessel',
            value: 'vehicleStationaryAndSecure',
            checked: riskBuffer?.vehicleStationaryAndSecure
                ? riskBuffer.vehicleStationaryAndSecure === 'on'
                : riskAnalysis?.vehicleStationaryAndSecure,
            handleChange: handleDgrFieldChange('vehicleStationaryAndSecure'),
            description: (
                <small>
                    <div>Conduct SAP prior to approaching the vessel.</div>
                    <div>
                        Check for fittings on the vessel that could damage the
                        CRV when coming alongside.
                    </div>
                </small>
            ),
        },
    ]

    // Stub for handleEditorChange
    const handleEditorChange = (newContent: any) => {
        setContent(newContent)
    }

    // Stub for handleSaveRisk; adjust logic as needed
    const handleSaveRisk = async () => {
        if (currentRisk && currentRisk.id) {
            if (offline) {
                await riskFactorModel.save({
                    id: currentRisk.id,
                    type: 'DangerousGoods',
                    title: currentRisk.title,
                    impact: currentRisk?.impact || 'Low',
                    probability: currentRisk?.probability || 5,
                    mitigationStrategy: {
                        nodes:
                            currentStrategies.length > 0
                                ? currentStrategies
                                : [],
                    },
                    dangerousGoodsChecklistID:
                        currentTrip.dangerousGoodsChecklist.id,
                })
                setOpenRiskDialog(false)
            } else {
                updateRiskFactor({
                    variables: {
                        input: {
                            id: currentRisk.id,
                            type: 'DangerousGoods',
                            title: currentRisk.title,
                            impact: currentRisk?.impact || 'Low',
                            probability: currentRisk?.probability || 5,
                            mitigationStrategy:
                                currentStrategies.length > 0
                                    ? currentStrategies
                                          .map((s: any) => s.id)
                                          .join(',')
                                    : '',
                            dangerousGoodsChecklistID:
                                currentTrip.dangerousGoodsChecklist.id,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                const data = await riskFactorModel.save({
                    id: generateUniqueId(),
                    type: 'DangerousGoods',
                    title: currentRisk.title,
                    impact: currentRisk?.impact || 'Low',
                    probability: currentRisk?.probability || 5,
                    mitigationStrategy: {
                        nodes:
                            currentStrategies.length > 0
                                ? currentStrategies
                                : [],
                    },
                    dangerousGoodsChecklistID:
                        currentTrip.dangerousGoodsChecklist.id,
                    vesselID: vesselID,
                })
                setOpenRiskDialog(false)

                // Update the UI with the new risk factor
                setAllRiskFactors([
                    ...allRiskFactors,
                    {
                        id: data.id,
                        title: currentRisk.title,
                        impact: currentRisk.impact,
                        probability: currentRisk.probability,
                        mitigationStrategy: {
                            nodes: currentStrategies,
                        },
                    },
                ])
                if (!allRisks.find((r: any) => r.value === currentRisk.title)) {
                    setAllRisks([
                        ...allRisks,
                        { value: currentRisk.title, label: currentRisk.title },
                    ])
                }
                // Safely update riskAnalysis with the new risk factor
                setRiskAnalysis({
                    ...riskAnalysis,
                    riskFactors: {
                        nodes: [
                            ...(riskAnalysis?.riskFactors?.nodes || []),
                            {
                                id: data.id,
                                title: currentRisk.title,
                                impact: currentRisk.impact,
                                probability: currentRisk.probability,
                                mitigationStrategy: {
                                    nodes: currentStrategies,
                                },
                            },
                        ],
                    },
                })
            } else {
                createRiskFactor({
                    variables: {
                        input: {
                            type: 'DangerousGoods',
                            title: currentRisk.title,
                            impact: currentRisk?.impact || 'Low',
                            probability: currentRisk?.probability || 5,
                            mitigationStrategy:
                                currentStrategies.length > 0
                                    ? currentStrategies
                                          .map((s: any) => s.id)
                                          .join(',')
                                    : '',
                            dangerousGoodsChecklistID:
                                currentTrip.dangerousGoodsChecklist.id,
                            vesselID: vesselID,
                        },
                    },
                })
            }
        }
    }

    // Query hooks for risk factors and risk analysis
    const [getRiskFactors] = useLazyQuery(GetRiskFactors, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (data) => {
            try {
                if (!data?.readRiskFactors?.nodes) {
                    setAllRisks([])
                    setAllRiskFactors([])
                    return
                }

                const titles = data.readRiskFactors.nodes
                    .map((risk: any) => risk.title)
                    .filter(Boolean)
                const uniqueTitles = Array.from(new Set(titles))
                const risks = uniqueTitles.map((title: any) => ({
                    label: title,
                    value: title,
                }))

                setAllRisks(risks)
                setAllRiskFactors(data.readRiskFactors.nodes)
            } catch (error) {
                console.error('Error processing risk factors:', error)
                setAllRisks([])
                setAllRiskFactors([])
            }
        },
        onError: (error) => {
            console.error('onError', error)
            setAllRisks([])
            setAllRiskFactors([])
        },
    })

    const [getRiskAnalysis] = useLazyQuery(GetOneDangerousGoodsChecklist, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (data) => {
            setRiskAnalysis(
                data.readOneDangerousGoodsChecklist || {
                    riskFactors: { nodes: [] },
                },
            )
            if (!riskBuffer) {
                setRiskBuffer({
                    vesselSecuredToWharf:
                        data.readOneDangerousGoodsChecklist
                            ?.vesselSecuredToWharf == true
                            ? 'on'
                            : 'off',
                    bravoFlagRaised:
                        data.readOneDangerousGoodsChecklist?.bravoFlagRaised ==
                        true
                            ? 'on'
                            : 'off',
                    twoCrewLoadingVessel:
                        data.readOneDangerousGoodsChecklist
                            ?.twoCrewLoadingVessel == true
                            ? 'on'
                            : 'off',
                    fireHosesRiggedAndReady:
                        data.readOneDangerousGoodsChecklist
                            ?.fireHosesRiggedAndReady == true
                            ? 'on'
                            : 'off',
                    noSmokingSignagePosted:
                        data.readOneDangerousGoodsChecklist
                            ?.noSmokingSignagePosted == true
                            ? 'on'
                            : 'off',
                    spillKitAvailable:
                        data.readOneDangerousGoodsChecklist
                            ?.spillKitAvailable == true
                            ? 'on'
                            : 'off',
                    fireExtinguishersAvailable:
                        data.readOneDangerousGoodsChecklist
                            ?.fireExtinguishersAvailable == true
                            ? 'on'
                            : 'off',
                    dgDeclarationReceived:
                        data.readOneDangerousGoodsChecklist
                            ?.dgDeclarationReceived == true
                            ? 'on'
                            : 'off',
                    loadPlanReceived:
                        data.readOneDangerousGoodsChecklist?.loadPlanReceived ==
                        true
                            ? 'on'
                            : 'off',
                    msdsAvailable:
                        data.readOneDangerousGoodsChecklist?.msdsAvailable ==
                        true
                            ? 'on'
                            : 'off',
                    anyVehiclesSecureToVehicleDeck:
                        data.readOneDangerousGoodsChecklist
                            ?.anyVehiclesSecureToVehicleDeck == true
                            ? 'on'
                            : 'off',
                    safetyAnnouncement:
                        data.readOneDangerousGoodsChecklist
                            ?.safetyAnnouncement == true
                            ? 'on'
                            : 'off',
                    vehicleStationaryAndSecure:
                        data.readOneDangerousGoodsChecklist
                            ?.vehicleStationaryAndSecure == true
                            ? 'on'
                            : 'off',
                    memberID: data.readOneDangerousGoodsChecklist?.member?.id,
                })
            }
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    // Mutation hooks for risk factors and mitigation strategies
    const [createMitigationStrategy] = useMutation(CreateMitigationStrategy, {
        onCompleted: (data) => {
            setCurrentStrategies([
                ...currentStrategies,
                { id: data.createMitigationStrategy.id, strategy: content },
            ])
            setContent('')
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [updateMitigationStrategy] = useMutation(UpdateMitigationStrategy, {
        onCompleted: (data) => {},
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [createRiskFactor] = useMutation(CreateRiskFactor, {
        onCompleted: (data) => {
            
            setOpenRiskDialog(false)
            setAllRiskFactors([
                ...allRiskFactors,
                {
                    id: data.createRiskFactor.id,
                    title: currentRisk.title,
                    impact: currentRisk.impact,
                    probability: currentRisk.probability,
                    mitigationStrategy: { nodes: currentStrategies },
                },
            ])
            if (
                Array.isArray(allRisks) &&
                !allRisks.find((r: any) => r.value === currentRisk.title)
            ) {
                setAllRisks([
                    ...allRisks,
                    { value: currentRisk.title, label: currentRisk.title },
                ])
            }
            // Safely update riskAnalysis with the new risk factor
            setRiskAnalysis({
                ...riskAnalysis,
                riskFactors: {
                    nodes: [
                        ...(riskAnalysis?.riskFactors?.nodes || []),
                        {
                            id: data.createRiskFactor.id,
                            title: currentRisk.title,
                            impact: currentRisk.impact,
                            probability: currentRisk.probability,
                            mitigationStrategy: { nodes: currentStrategies },
                        },
                    ],
                },
            })
            // Safely get recommended strategies with proper null checks
            const matchingRiskFactors =
                allRiskFactors?.filter(
                    (r: any) =>
                        r.title === currentRisk.title &&
                        r.mitigationStrategy?.nodes?.length > 0,
                ) || []

            // Get all matching nodes from all matching risk factors
            const allMatchingNodes = matchingRiskFactors.flatMap(
                (r: any) => r.mitigationStrategy?.nodes || [],
            )

            const mappedStrategies = allMatchingNodes.map((s: any) => ({
                id: s.id,
                strategy: s.strategy,
            }))

            // Remove duplicates by ID
            const uniqueStrategies = Array.from(
                new Map(
                    mappedStrategies.map((item: any) => [item.id, item]),
                ).values(),
            )

            setRecommendedStratagies(uniqueStrategies)
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [updateRiskFactor] = useMutation(UpdateRiskFactor, {
        onCompleted: (/* data - unused but required by Apollo */) => {
            setOpenRiskDialog(false)
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const handleRiskValue = (v: any) => {
        if (!v) return

        setCurrentRisk({ ...currentRisk, title: v?.value })
        setRiskValue({ value: v.value, label: v.value })
        if (
            allRiskFactors?.filter(
                (risk: any) =>
                    risk.title === v.value &&
                    risk.mitigationStrategy?.nodes?.length > 0,
            ).length > 0
        ) {
            // Safely get recommended strategies with proper null checks
            const matchingRiskFactors =
                allRiskFactors?.filter(
                    (r: any) =>
                        r.title === v.value &&
                        r.mitigationStrategy?.nodes?.length > 0,
                ) || []

            // Get all matching nodes from all matching risk factors
            const allMatchingNodes = matchingRiskFactors.flatMap(
                (r: any) => r.mitigationStrategy?.nodes || [],
            )

            const mappedStrategies = allMatchingNodes.map((s: any) => ({
                id: s.id,
                strategy: s.strategy,
            }))

            // Remove duplicates by ID
            const uniqueStrategies = Array.from(
                new Map(
                    mappedStrategies.map((item: any) => [item.id, item]),
                ).values(),
            )

            setRecommendedStratagies(uniqueStrategies)
        } else {
            setRecommendedStratagies(false)
        }
    }

    // This function is used for creating new risks from user input
    // Currently not directly called but kept for future use
    const handleCreateRisk = (inputValue: any) => {
        if (!inputValue) return

        setCurrentRisk({ ...currentRisk, title: inputValue })
        setRiskValue({ value: inputValue, label: inputValue })
        if (allRisks && Array.isArray(allRisks) && allRisks.length > 0) {
            const risk = [...allRisks, { value: inputValue, label: inputValue }]
            setAllRisks(risk)
        } else {
            setAllRisks([{ value: inputValue, label: inputValue }])
        }
    }

    const handleDeleteRisk = async () => {
        if (offline) {
            await riskFactorModel.save({
                id: riskToDelete.id,
                vesselID: 0,
                dangerousGoodsChecklistID: 0,
            })
            setOpenRiskDialog(false)
        } else {
            updateRiskFactor({
                variables: {
                    input: {
                        id: riskToDelete.id,
                        vesselID: 0,
                        dangerousGoodsChecklistID: 0,
                    },
                },
            })
        }
        // Safely update riskAnalysis by filtering out the deleted risk
        setRiskAnalysis({
            ...riskAnalysis,
            riskFactors: {
                nodes: (riskAnalysis?.riskFactors?.nodes || []).filter(
                    (risk: any) => risk.id !== riskToDelete.id,
                ),
            },
        })
        setOpenDeleteConfirmation(false)
    }

    const handleSetCurrentStrategies = (strategy: any) => {
        if (currentStrategies.length > 0) {
            if (currentStrategies.find((s: any) => s.id === strategy.id)) {
                setCurrentStrategies(
                    currentStrategies.filter((s: any) => s.id !== strategy.id),
                )
            } else {
                setCurrentStrategies([...currentStrategies, strategy])
            }
        } else {
            setCurrentStrategies([strategy])
        }
    }

    const handleNewStratagy = async () => {
        if (content) {
            if (offline) {
                const data = await mitigationStrategyModel.save({
                    id: generateUniqueId(),
                    strategy: content,
                })
                setCurrentStrategies([
                    ...currentStrategies,
                    { id: data.id, strategy: content },
                ])
                setContent('')
                setCurrentRisk({ ...currentRisk, mitigationStrategy: data.id })
            } else {
                createMitigationStrategy({
                    variables: {
                        input: { strategy: content },
                    },
                })
            }
        }
        setOpenRecommendedstrategy(false)
    }

    // Another version of handleSetRiskValue for use on risk selection
    // Currently not directly called but kept for future use or reference
    const handleSetRiskValue = (v: any) => {
        if (!v) return

        setRiskValue({ value: v.title, label: v.title })
        if (
            allRiskFactors?.filter(
                (risk: any) =>
                    risk.title === v.title &&
                    risk.mitigationStrategy?.nodes?.length > 0,
            ).length > 0
        ) {
            // Safely get recommended strategies with proper null checks
            const matchingRiskFactors =
                allRiskFactors?.filter(
                    (r: any) =>
                        r.title === v.title &&
                        r.mitigationStrategy?.nodes?.length > 0,
                ) || []

            // Get all matching nodes from all matching risk factors
            const allMatchingNodes = matchingRiskFactors.flatMap(
                (r: any) => r.mitigationStrategy?.nodes || [],
            )

            const mappedStrategies = allMatchingNodes.map((s: any) => ({
                id: s.id,
                strategy: s.strategy,
            }))

            // Remove duplicates by ID
            const uniqueStrategies = Array.from(
                new Map(
                    mappedStrategies.map((item: any) => [item.id, item]),
                ).values(),
            )

            setRecommendedStratagies(uniqueStrategies)
        } else {
            setRecommendedStratagies(false)
        }
    }

    useEffect(() => {
        setAllChecked(checkFields.every((field) => field.checked))
    }, [checkFields])

    return (
        <>
            <RiskAnalysisSheet
                open={open}
                onOpenChange={onOpenChange}
                onSidebarClose={onSidebarClose}
                title="Risk analysis"
                subtitle="Dangerous goods"
                checkFields={checkFields.map((field: any) => ({
                    name: field.name,
                    label: field.label,
                    value: field.value,
                    checked: checkFieldVal(field.value),
                    handleChange: (checked: boolean) =>
                        field.handleChange(checked),
                    description: field.description,
                }))}
                riskFactors={riskAnalysis?.riskFactors?.nodes || []}
                crewMembers={members}
                selectedAuthor={
                    members.find(
                        (member: any) => member.value == riskBuffer?.memberID,
                    ) || null
                }
                onAuthorChange={(value: any) => {
                    if (value) {
                        updateDangerousGoodsChecklist({
                            variables: {
                                input: {
                                    memberID: value.value,
                                    id: currentTrip.dangerousGoodsChecklist.id,
                                },
                            },
                        })
                    }
                }}
                canEdit={editDGR}
                canDeleteRisks={editDGR}
                onRiskClick={(risk: any) => {
                    if (!editDGR) {
                        toast({
                            title: 'Permission Denied',
                            description:
                                'You do not have permission to edit this section',
                            variant: 'destructive',
                        })
                        return
                    }
                    setCurrentRisk(risk)
                    setRiskValue({ value: risk.title, label: risk.title })
                    setOpenRiskDialog(true)
                    setCurrentStrategies(risk.mitigationStrategy?.nodes || [])
                }}
                onAddRiskClick={() => {
                    if (!editDGR) {
                        toast({
                            title: 'Permission Denied',
                            description:
                                'You do not have permission to edit this section',
                            variant: 'destructive',
                        })
                        return
                    }
                    setCurrentRisk({})
                    setContent('')
                    setRiskValue(null)
                    setOpenRiskDialog(true)
                    setCurrentStrategies([])
                }}
                onRiskDelete={() => {
                    handleDeleteRisk()
                }}
                setAllChecked={setAllChecked}
                selectedEvent
                currentTrip
            />
            <RiskDialog
                open={openRiskDialog}
                onOpenChange={setOpenRiskDialog}
                currentRisk={currentRisk}
                onSave={handleSaveRisk}
                riskOptions={allRisks}
                riskValue={riskValue}
                onRiskValueChange={handleRiskValue}
                riskImpacts={riskImpacts}
                onRiskImpactChange={(value: any) =>
                    setCurrentRisk({
                        ...currentRisk,
                        impact: value?.value,
                    })
                }
                onRiskProbabilityChange={(value: number) =>
                    setCurrentRisk({
                        ...currentRisk,
                        probability: value,
                    })
                }
                currentStrategies={currentStrategies}
                content={content}
                onAddStrategyClick={() => setOpenRecommendedstrategy(true)}
            />
            <StrategyDialog
                open={openRecommendedstrategy}
                onOpenChange={setOpenRecommendedstrategy}
                onSave={handleNewStratagy}
                currentRisk={currentRisk}
                recommendedStrategies={recommendedStratagies}
                currentStrategies={currentStrategies}
                onStrategySelect={(strategy: any) => {
                    setRecommendedstrategy(strategy)
                    handleSetCurrentStrategies(strategy)
                    setUpdateStrategy(false)
                }}
                content={content}
                onEditorChange={handleEditorChange}
            />
            <AlertDialogNew
                openDialog={openDeleteConfirmation}
                setOpenDialog={setOpenDeleteConfirmation}
                handleCreate={handleDeleteRisk}
                actionText="Delete"
                title="Confirm Deletion"
                variant="warning"
                size="lg"
                position="center"
                showIcon={true}>
                <P>
                    Are you sure you want to delete this risk? This action
                    cannot be undone.
                </P>
            </AlertDialogNew>
        </>
    )
}
