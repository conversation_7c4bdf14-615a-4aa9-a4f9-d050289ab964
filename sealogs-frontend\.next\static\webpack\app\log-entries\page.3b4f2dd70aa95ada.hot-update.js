"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/events.tsx":
/*!***************************************!*\
  !*** ./src/app/ui/logbook/events.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Events; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _forms_vessel_rescue__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./forms/vessel-rescue */ \"(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue.tsx\");\n/* harmony import */ var _forms_person_rescue__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./forms/person-rescue */ \"(app-pages-browser)/./src/app/ui/logbook/forms/person-rescue.tsx\");\n/* harmony import */ var _forms_restricted_visibility__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./forms/restricted-visibility */ \"(app-pages-browser)/./src/app/ui/logbook/forms/restricted-visibility.tsx\");\n/* harmony import */ var _forms_bar_crossing__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./forms/bar-crossing */ \"(app-pages-browser)/./src/app/ui/logbook/forms/bar-crossing.tsx\");\n/* harmony import */ var _forms_passenger_drop_facility__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forms/passenger-drop-facility */ \"(app-pages-browser)/./src/app/ui/logbook/forms/passenger-drop-facility.tsx\");\n/* harmony import */ var _forms_tasking__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./forms/tasking */ \"(app-pages-browser)/./src/app/ui/logbook/forms/tasking.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _forms_crew_training_event__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./forms/crew-training-event */ \"(app-pages-browser)/./src/app/ui/logbook/forms/crew-training-event.tsx\");\n/* harmony import */ var _forms_supernumerary_event__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./forms/supernumerary-event */ \"(app-pages-browser)/./src/app/ui/logbook/forms/supernumerary-event.tsx\");\n/* harmony import */ var _forms_passenger_vehicle_pick_drop__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./forms/passenger-vehicle-pick-drop */ \"(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! lodash/uniqueId */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqueId.js\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var _forms_refuelling_bunkering__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./forms/refuelling-bunkering */ \"(app-pages-browser)/./src/app/ui/logbook/forms/refuelling-bunkering.tsx\");\n/* harmony import */ var _app_lib_vesselTypes__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/vesselTypes */ \"(app-pages-browser)/./src/app/lib/vesselTypes.tsx\");\n/* harmony import */ var _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/app/lib/logbook-configuration */ \"(app-pages-browser)/./src/app/lib/logbook-configuration/index.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _forms_InfringementNotices__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./forms/InfringementNotices */ \"(app-pages-browser)/./src/app/ui/logbook/forms/InfringementNotices.tsx\");\n/* harmony import */ var _forms_trip_update__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./forms/trip-update */ \"(app-pages-browser)/./src/app/ui/logbook/forms/trip-update.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/accordion */ \"(app-pages-browser)/./src/components/ui/accordion.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _radio_logs_schedule__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./radio-logs-schedule */ \"(app-pages-browser)/./src/app/ui/logbook/radio-logs-schedule.tsx\");\n/* harmony import */ var _incident_record_incident_record_form__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ../incident-record/incident-record-form */ \"(app-pages-browser)/./src/app/ui/incident-record/incident-record-form.tsx\");\n/* harmony import */ var _forms_pilot_transfer__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./forms/pilot-transfer */ \"(app-pages-browser)/./src/app/ui/logbook/forms/pilot-transfer.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Events(param) {\n    let { currentTrip, logBookConfig, updateTripReport, locked, geoLocations, tripReport, crewMembers, masterID, vessel, vessels, offline = false, setSelectedRow, setCurrentEventType, setCurrentStop, currentEventType, currentStop, tripReport_Stops, setTripReport_Stops, displayDangerousGoodsPvpd = false, displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpdSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGRPVPD, setSelectedDGRPVPD, fuelLogs, logBookStartDate } = param;\n    var _currentTrip_tripReport_Stops, _currentTrip_tripEvents_nodes, _currentTrip_tripEvents, _currentTrip_tripReport_Stops_nodes, _currentTrip_tripReport_Stops1, _currentTrip_tripEvents_nodes1, _currentTrip_tripEvents1, _currentTrip_tripReport_Stops_nodes1, _currentTrip_tripReport_Stops2, _currentTrip_tripReport_Stops3;\n    _s();\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [openEventModal, setOpenEventModal] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [taskingEvents, setTaskingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [accordionValue, setAccordionValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const vesselID = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams)().get(\"vesselID\") || \"0\";\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [edit_tripActivity, setEdit_tripActivity] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [displayRadioLogs, setDisplayRadioLogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [activityTypeOptions, setActivityTypeOptions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.hasPermission)(process.env.EDIT_LOGBOOKENTRY_ACTIVITY || \"EDIT_LOGBOOKENTRY_ACTIVITY\", permissions)) {\n                setEdit_tripActivity(true);\n            } else {\n                setEdit_tripActivity(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        init_permissions();\n        initData();\n    }, [\n        permissions\n    ]);\n    const initData = ()=>{\n        var _logBookConfig_customisedLogBookComponents;\n        const combinedFields = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents.nodes.filter((section)=>section.componentClass === \"SeaLogs\\\\EventType_LogBookComponent\" || section.componentClass === \"EventType_LogBookComponent\").reduce((acc, section)=>{\n            acc = acc.concat(section.customisedComponentFields.nodes);\n            return acc;\n        }, []);\n        const hasRescueType = combinedFields === null || combinedFields === void 0 ? void 0 : combinedFields.find((field)=>field.fieldName === \"VesselRescue\" || field.fieldName === \"HumanRescue\");\n        if (logBookConfig) {\n            const eventList = hasRescueType ? combinedFields.filter((field)=>!hasParent(field) && field.status !== \"Off\") : combinedFields === null || combinedFields === void 0 ? void 0 : combinedFields.filter((field)=>!hasParent(field) && field.status !== \"Off\" && field.fieldName !== \"TaskingStartUnderway\" && field.fieldName !== \"TaskingOnScene\" && field.fieldName !== \"TaskingOnTow\" && field.fieldName !== \"TaskingPaused\" && field.fieldName !== \"TaskingResumed\" && field.fieldName !== \"TaskingComplete\" && field.fieldName !== \"DangerousGoodsSailing\");\n            const filteredEvents = eventList === null || eventList === void 0 ? void 0 : eventList.map((event)=>({\n                    label: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_2__.getFieldName)(event).replace(/([a-z])([A-Z])/g, \"$1 $2\").replace(\"Passenger Arrival\", \"Arrival\").replace(\"Passenger Departure\", \"Departure\"),\n                    value: event.fieldName\n                })).filter((event, index, self)=>index === self.findIndex((e)=>e.value === event.value)).filter((event)=>// event?.value !== 'VesselRescue' &&\n                // event?.value !== 'HumanRescue' &&\n                // event?.value !== 'Supernumerary' &&\n                !isTowingField(event.value)).filter((event)=>checkVesselType(event.value));\n            // Add Incident Record as a custom activity type\n            // Incident Record is available for all vessel types\n            filteredEvents.push({\n                label: \"Incident Record\",\n                value: \"IncidentRecord\"\n            });\n            // Add Infringement Notices as a custom activity type if vessel type allows it\n            // InfringementNotices is only available for vessel types 0 and 1\n            const vesselTypeID = _app_lib_vesselTypes__WEBPACK_IMPORTED_MODULE_16__[\"default\"].findIndex((type)=>type == (vessel === null || vessel === void 0 ? void 0 : vessel.vesselType));\n            if ([\n                0,\n                1\n            ].includes(vesselTypeID)) {\n                filteredEvents.push({\n                    label: \"Infringement Notices\",\n                    value: \"InfringementNotice\"\n                });\n            }\n            if (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.hasPermission)(\"RECORD_TRAINING\", permissions)) {\n                setEvents(sortFilteredEvents(filteredEvents));\n            } else {\n                var _filteredEvents_filter;\n                setEvents(sortFilteredEvents((_filteredEvents_filter = filteredEvents === null || filteredEvents === void 0 ? void 0 : filteredEvents.filter((event)=>event.value !== \"CrewTraining\")) !== null && _filteredEvents_filter !== void 0 ? _filteredEvents_filter : []));\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        initData();\n    }, [\n        logBookConfig\n    ]);\n    const checkVesselType = (field)=>{\n        const vesselTypeID = _app_lib_vesselTypes__WEBPACK_IMPORTED_MODULE_16__[\"default\"].findIndex((type)=>type == (vessel === null || vessel === void 0 ? void 0 : vessel.vesselType));\n        const config = _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_17__.SLALL_LogBookFields.find((localField)=>localField.componentClass === \"EventType_LogBookComponent\");\n        const isVesselType = config === null || config === void 0 ? void 0 : config.items.find((localField)=>field === localField.value && localField.vesselType.includes(vesselTypeID));\n        return isVesselType ? true : false;\n    };\n    const sortFilteredEvents = (events)=>{\n        var _currentTrip_tripEvents_nodes, _currentTrip_tripEvents;\n        // Always ensure tasking events are available in the base events array\n        const taskingEvents = [\n            \"TaskingStartUnderway\",\n            \"TaskingOnScene\",\n            \"TaskingOnTow\",\n            \"TaskingComplete\",\n            \"TaskingPaused\",\n            \"TaskingResumed\"\n        ];\n        // Add missing tasking events to the events array\n        const eventsWithTasking = [\n            ...events\n        ];\n        taskingEvents.forEach((taskingType)=>{\n            if (!eventsWithTasking.find((event)=>event.value === taskingType)) {\n                eventsWithTasking.push({\n                    label: taskingType.replace(/([a-z])([A-Z])/g, \"$1 $2\"),\n                    value: taskingType\n                });\n            }\n        });\n        if (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes = _currentTrip_tripEvents.nodes) === null || _currentTrip_tripEvents_nodes === void 0 ? void 0 : _currentTrip_tripEvents_nodes.find((event)=>{\n            var _event_eventType_Tasking, _event_eventType_Tasking1;\n            return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\" && ((_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.status) === \"Open\";\n        })) {\n            var _currentTrip_tripEvents_nodes_filter, _currentTrip_tripEvents_nodes_filter1;\n            const openTask = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter = currentTrip.tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\";\n            })) === null || _currentTrip_tripEvents_nodes_filter === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter.length;\n            const pausedTask = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter1 = currentTrip.tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingPaused\";\n            })) === null || _currentTrip_tripEvents_nodes_filter1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter1.length;\n            const sortedEvents = [\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingStartUnderway\" && openTask - pausedTask < 1).map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingOnScene\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingOnTow\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingComplete\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingPaused\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingResumed\" && pausedTask > 0).map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>!event.value.includes(\"Tasking\"))\n            ];\n            return sortedEvents;\n        }\n        return eventsWithTasking;\n    };\n    /*const colourStyles: StylesConfig = {\r\n        option: (\r\n            styles: any,\r\n            {\r\n                data,\r\n                isDisabled,\r\n                isFocused,\r\n                isSelected,\r\n            }: { data: any; isDisabled: any; isFocused: any; isSelected: any },\r\n        ) => {\r\n            const color = data.color\r\n            return {\r\n                ...styles,\r\n                backgroundColor: isDisabled\r\n                    ? undefined\r\n                    : isSelected\r\n                      ? data.bgColor\r\n                      : isFocused\r\n                        ? data.bgColor\r\n                        : data.bgColor + '60',\r\n                color: data.color,\r\n            }\r\n        },\r\n        singleValue: (styles: any, data: any) => ({\r\n            ...styles,\r\n            color: events.find((option: any) => option.value == data.data.value)\r\n                ?.color,\r\n        }),\r\n    }*/ const formatTime = (time)=>time.slice(0, 5);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        var _currentTrip_tripEvents_nodes_filter, _currentTrip_tripEvents;\n        const taskingEvents = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter = _currentTrip_tripEvents.nodes.filter((event)=>{\n            var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2;\n            return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && ((event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\" || (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.type) === \"TaskingPaused\") && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.status) === \"Open\";\n        })) === null || _currentTrip_tripEvents_nodes_filter === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter.length;\n        setTaskingEvents(taskingEvents);\n    }, [\n        currentTrip\n    ]);\n    const hasParent = (field)=>{\n        const config = _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_17__.SLALL_LogBookFields.find((localField)=>localField.componentClass === \"EventType_LogBookComponent\");\n        const hasGroup = config === null || config === void 0 ? void 0 : config.items.find((localField)=>field.fieldName === localField.value && localField.groupTo);\n        return hasGroup ? true : false;\n    };\n    const isTowingField = (field)=>{\n        const config = _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_17__.SLALL_LogBookFields.find((localField)=>localField.componentClass === \"EventType_LogBookComponent\");\n        const isTowingCategory = config === null || config === void 0 ? void 0 : config.items.find((localField)=>field === localField.value && localField.type === \"TowingSubCategory\");\n        return isTowingCategory ? true : false;\n    };\n    const handleEventChange = (event)=>{\n        setCurrentEvent(false);\n        setCurrentStop(false);\n        setTripReport_Stops(false);\n        setDisplayDangerousGoodsPvpd(false);\n        setDisplayDangerousGoodsPvpdSailing(null);\n        fetchActivityTypes();\n        setCurrentEventType(event);\n    };\n    // const handleSetOpenEventModal = () => {\n    // setOpenEventModal(!openEventModal)\n    // }\n    const handleSetCurrentEventType = ()=>{\n        setCurrentEventType(false);\n        // Reset accordion state to properly close it\n        setAccordionValue(\"\");\n        setSelectedRow(0);\n        setCurrentEvent(false);\n        setCurrentStop(false);\n    // setOpenEventModal(false)\n    };\n    const previousDropEvent = (currentEvent)=>{\n        var _currentTrip_tripEvents;\n        const previousEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id));\n        return previousEvent;\n    };\n    const mainTaskingEvent = (currentEvent)=>{\n        var _currentTrip_tripEvents;\n        const mainEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>{\n            var _event_eventType_Tasking;\n            return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\";\n        });\n        return mainEvent;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (events) {\n            var _currentTrip_tripEvents_nodes_filter, _currentTrip_tripEvents_nodes, _currentTrip_tripEvents;\n            let options = [];\n            if (taskingEvents === 0) {\n                options = events.filter((event)=>(event === null || event === void 0 ? void 0 : event.value) !== \"TaskingOnScene\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingOnTow\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingPaused\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingResumed\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingComplete\");\n                // Ensure TaskingStartUnderway is always available when no tasking events are open\n                const taskingStartUnderwayOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingStartUnderway\");\n                if (taskingStartUnderwayOption && !options.find((option)=>option.value === \"TaskingStartUnderway\")) {\n                    options.push(taskingStartUnderwayOption);\n                } else if (!taskingStartUnderwayOption) {\n                    // If TaskingStartUnderway is not in the events array, create it manually\n                    options.push({\n                        label: \"Tasking Start Underway\",\n                        value: \"TaskingStartUnderway\"\n                    });\n                }\n            } else {\n                var _currentTrip_tripEvents1, _currentTrip_tripEvents2, _currentTrip_tripEvents3;\n                const taskingOpen = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : _currentTrip_tripEvents1.nodes.filter((event)=>{\n                    var _event_eventType_Tasking, _event_eventType_Tasking1;\n                    return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.status) === \"Open\";\n                });\n                const taskingPaused = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : _currentTrip_tripEvents2.nodes.filter((event)=>{\n                    var _event_eventType_Tasking;\n                    return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingPaused\";\n                });\n                const taskingResumed = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents3 = currentTrip.tripEvents) === null || _currentTrip_tripEvents3 === void 0 ? void 0 : _currentTrip_tripEvents3.nodes.filter((event)=>{\n                    var _event_eventType_Tasking;\n                    return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingResumed\";\n                });\n                if ((taskingOpen === null || taskingOpen === void 0 ? void 0 : taskingOpen.length) > 0) {\n                    if ((taskingPaused === null || taskingPaused === void 0 ? void 0 : taskingPaused.length) === (taskingResumed === null || taskingResumed === void 0 ? void 0 : taskingResumed.length)) {\n                        options = events.filter((event)=>(event === null || event === void 0 ? void 0 : event.value) !== \"TaskingResumed\");\n                    }\n                    if ((taskingPaused === null || taskingPaused === void 0 ? void 0 : taskingPaused.length) > (taskingResumed === null || taskingResumed === void 0 ? void 0 : taskingResumed.length)) {\n                        options = events.filter((event)=>event.value !== \"TaskingOnScene\" && event.value !== \"TaskingOnTow\" && event.value !== \"TaskingPaused\");\n                    }\n                } else {\n                    options = events.filter((event)=>event.value !== \"TaskingOnScene\" && event.value !== \"TaskingOnTow\" && event.value !== \"TaskingPaused\" && event.value !== \"TaskingResumed\" && event.value !== \"TaskingComplete\");\n                    // Ensure TaskingStartUnderway is available when no open tasking events exist\n                    const taskingStartUnderwayOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingStartUnderway\");\n                    if (taskingStartUnderwayOption && !options.find((option)=>option.value === \"TaskingStartUnderway\")) {\n                        options.push(taskingStartUnderwayOption);\n                    } else if (!taskingStartUnderwayOption) {\n                        // If TaskingStartUnderway is not in the events array, create it manually\n                        options.push({\n                            label: \"Tasking Start Underway\",\n                            value: \"TaskingStartUnderway\"\n                        });\n                    }\n                }\n            }\n            // When taskingPaused > 0, ensure TaskingResumed and TaskingStartUnderway are available\n            const taskingPausedCount = (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes = _currentTrip_tripEvents.nodes) === null || _currentTrip_tripEvents_nodes === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter = _currentTrip_tripEvents_nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingPaused\";\n            })) === null || _currentTrip_tripEvents_nodes_filter === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter.length) || 0;\n            if (taskingPausedCount > 0) {\n                // Find TaskingResumed and TaskingStartUnderway from the original events array\n                const taskingResumedOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingResumed\");\n                const taskingStartUnderwayOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingStartUnderway\");\n                // Add TaskingResumed if it exists in events but not in current options\n                if (taskingResumedOption && !options.find((option)=>option.value === \"TaskingResumed\")) {\n                    options.push(taskingResumedOption);\n                }\n                // Add TaskingStartUnderway if it exists in events but not in current options\n                if (taskingStartUnderwayOption && !options.find((option)=>option.value === \"TaskingStartUnderway\")) {\n                    options.push(taskingStartUnderwayOption);\n                } else if (!taskingStartUnderwayOption) {\n                    // If TaskingStartUnderway is not in the events array, create it manually\n                    options.push({\n                        label: \"Tasking Start Underway\",\n                        value: \"TaskingStartUnderway\"\n                    });\n                }\n            }\n            options = options.map((option)=>{\n                if (option.value.includes(\"Tasking\") && option.value !== \"TaskingStartUnderway\" && !option.className) {\n                    return {\n                        ...option,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    };\n                }\n                return option;\n            });\n            // Remove duplicate by checking the options.value\n            options = options.filter((option, index, self)=>index === self.findIndex((o)=>o.value === option.value));\n            // Remove InfringementNotices from options because there's already InfringementNotice (without an 's').\n            options = options.filter((option)=>option.value !== \"InfringementNotices\");\n            // Remove HumanRescue and VesselRescue from options since it's already included in Tasking\n            options = options.filter((option)=>option.value !== \"HumanRescue\" && option.value !== \"VesselRescue\");\n            setActivityTypeOptions(options);\n        }\n    }, [\n        events,\n        currentTrip,\n        taskingEvents\n    ]);\n    const fetchActivityTypes = ()=>{\n        initData();\n    };\n    // Memoized function to handle stop accordion item clicks\n    const handleStopAccordionItemClick = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((eventId)=>{\n        return ()=>{\n            // Toggle accordion state\n            if (accordionValue === \"stop_\".concat(eventId)) {\n                setAccordionValue(\"\");\n                setSelectedRow(0);\n                setCurrentEventType([]);\n                setCurrentEvent(false);\n                setCurrentStop(false);\n            } else {\n                var _currentTrip_tripReport_Stops;\n                setAccordionValue(\"stop_\".concat(eventId));\n                setSelectedRow(eventId);\n                setCurrentEventType({\n                    label: \"Passenger/vehicle pickup/drop off\",\n                    value: \"PassengerVehiclePickDrop\"\n                });\n                // Find the event by ID\n                const event = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops === void 0 ? void 0 : _currentTrip_tripReport_Stops.nodes.find((stop)=>stop.id === eventId);\n                setCurrentStop(event);\n                setDisplayDangerousGoodsPvpd(false);\n                setDisplayDangerousGoodsPvpdSailing(null);\n                setTripReport_Stops(false);\n            }\n        };\n    }, [\n        accordionValue,\n        currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops === void 0 ? void 0 : _currentTrip_tripReport_Stops.nodes,\n        setSelectedRow,\n        setCurrentEventType,\n        setCurrentEvent,\n        setCurrentStop,\n        setDisplayDangerousGoodsPvpd,\n        setDisplayDangerousGoodsPvpdSailing,\n        setTripReport_Stops\n    ]);\n    // Memoized function to generate stop display text\n    const getStopDisplayText = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((event)=>{\n        var _event_stopLocation, _event_stopLocation1;\n        return \"Passenger / Vehicle Pick & Drop - \".concat((event === null || event === void 0 ? void 0 : event.arriveTime) ? (event === null || event === void 0 ? void 0 : event.arriveTime) + \" (arr)\" : \"\", \" \").concat((event === null || event === void 0 ? void 0 : event.arriveTime) && (event === null || event === void 0 ? void 0 : event.departTime) ? \"-\" : \"\", \" \").concat((event === null || event === void 0 ? void 0 : event.departTime) ? (event === null || event === void 0 ? void 0 : event.departTime) + \" (dep)\" : \"\", \" \").concat((event === null || event === void 0 ? void 0 : (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.title) ? event === null || event === void 0 ? void 0 : (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.title : \"\");\n    }, []);\n    const shouldIndent = (event)=>{\n        var _event_eventType_Tasking;\n        return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) !== \"TaskingStartUnderway\";\n    };\n    const getEventLabel = (event)=>{\n        var _event_eventType_PassengerDropFacility_type_replace_replace, _event_eventType_PassengerDropFacility_type_replace, _event_eventType_PassengerDropFacility_type, _event_eventType_PassengerDropFacility, _event_eventType_Tasking;\n        return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type = _event_eventType_PassengerDropFacility.type) === null || _event_eventType_PassengerDropFacility_type === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace = _event_eventType_PassengerDropFacility_type.replace(/([a-z])([A-Z])/g, \"$1 $2\")) === null || _event_eventType_PassengerDropFacility_type_replace === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace_replace = _event_eventType_PassengerDropFacility_type_replace.replace(\"Passenger Arrival\", \"Arrival\")) === null || _event_eventType_PassengerDropFacility_type_replace_replace === void 0 ? void 0 : _event_eventType_PassengerDropFacility_type_replace_replace.replace(\"Passenger Departure\", \"Departure\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type.replace(/([a-z])([A-Z])/g, \"$1 $2\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"EventSupernumerary\" ? \"Supernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory.replace(/([a-z])([A-Z])/g, \"$1 $2\");\n    };\n    const getEventValue = (event)=>{\n        var _event_eventType_PassengerDropFacility, _event_eventType_Tasking;\n        return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility === void 0 ? void 0 : _event_eventType_PassengerDropFacility.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Supernumerary\" ? \"EventSupernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory;\n    };\n    const getFuelTotals = (fuelLogs)=>{\n        const totalFuel = fuelLogs.reduce((acc, log)=>{\n            return acc + (log === null || log === void 0 ? void 0 : log.fuelAdded);\n        }, 0);\n        const totalCost = fuelLogs.reduce((acc, log)=>{\n            return acc + (log === null || log === void 0 ? void 0 : log.fuelAdded) * (log === null || log === void 0 ? void 0 : log.costPerLitre);\n        }, 0);\n        return \" - Total Fuel Added: \" + totalFuel + \"L, Total Cost: $\" + totalCost;\n    };\n    const getEventDisplayText = (event)=>{\n        var _eventType_geoLocation;\n        const category = event.eventCategory;\n        const eventType = event[\"eventType_\".concat(category)];\n        const geoLocation = eventType === null || eventType === void 0 ? void 0 : (_eventType_geoLocation = eventType.geoLocation) === null || _eventType_geoLocation === void 0 ? void 0 : _eventType_geoLocation.title;\n        const title = eventType === null || eventType === void 0 ? void 0 : eventType.title;\n        switch(category){\n            case \"PassengerDropFacility\":\n                var _eventType_type_replace_replace, _eventType_type_replace, _eventType_type;\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.time) ? eventType.time + \" - \" : \"\") + (eventType === null || eventType === void 0 ? void 0 : (_eventType_type = eventType.type) === null || _eventType_type === void 0 ? void 0 : (_eventType_type_replace = _eventType_type.replace(/([a-z])([A-Z])/g, \"$1 $2\")) === null || _eventType_type_replace === void 0 ? void 0 : (_eventType_type_replace_replace = _eventType_type_replace.replace(\"Passenger Arrival\", \"Arrival\")) === null || _eventType_type_replace_replace === void 0 ? void 0 : _eventType_type_replace_replace.replace(\"Passenger Departure\", \"Departure\")) + (title ? \" - \" + title : \"\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"Tasking\":\n                var _eventType_type1;\n                return (eventType === null || eventType === void 0 ? void 0 : eventType.time) + \" - \" + (eventType === null || eventType === void 0 ? void 0 : (_eventType_type1 = eventType.type) === null || _eventType_type1 === void 0 ? void 0 : _eventType_type1.replace(/([a-z])([A-Z])/g, \"$1 $2\")) + (title ? \" - \" + title : \"\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"BarCrossing\":\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.time) ? eventType.time + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"RefuellingBunkering\":\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.date) ? dayjs__WEBPACK_IMPORTED_MODULE_21___default()(eventType.date).format(\"HH:mm\") + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"RestrictedVisibility\":\n                var _eventType_startLocation;\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.crossingTime) ? eventType.crossingTime + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + ((eventType === null || eventType === void 0 ? void 0 : (_eventType_startLocation = eventType.startLocation) === null || _eventType_startLocation === void 0 ? void 0 : _eventType_startLocation.title) ? \" - \" + eventType.startLocation.title : \"\");\n            case \"TripUpdate\":\n                var _event_tripUpdate, _event_tripUpdate_geoLocation, _event_tripUpdate1, _event_tripUpdate_geoLocation1, _event_tripUpdate2;\n                return (((_event_tripUpdate = event.tripUpdate) === null || _event_tripUpdate === void 0 ? void 0 : _event_tripUpdate.date) ? dayjs__WEBPACK_IMPORTED_MODULE_21___default()(event.tripUpdate.date).format(\"HH:mm\") + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_tripUpdate1 = event.tripUpdate) === null || _event_tripUpdate1 === void 0 ? void 0 : (_event_tripUpdate_geoLocation = _event_tripUpdate1.geoLocation) === null || _event_tripUpdate_geoLocation === void 0 ? void 0 : _event_tripUpdate_geoLocation.title) ? \" - \" + ((_event_tripUpdate2 = event.tripUpdate) === null || _event_tripUpdate2 === void 0 ? void 0 : (_event_tripUpdate_geoLocation1 = _event_tripUpdate2.geoLocation) === null || _event_tripUpdate_geoLocation1 === void 0 ? void 0 : _event_tripUpdate_geoLocation1.title) : \"\");\n            case \"EventSupernumerary\":\n                var _event_supernumerary, _event_supernumerary1, _event_supernumerary2;\n                return ((event === null || event === void 0 ? void 0 : (_event_supernumerary = event.supernumerary) === null || _event_supernumerary === void 0 ? void 0 : _event_supernumerary.briefingTime) ? (event === null || event === void 0 ? void 0 : (_event_supernumerary1 = event.supernumerary) === null || _event_supernumerary1 === void 0 ? void 0 : _event_supernumerary1.briefingTime) + \" - \" : \"\") + \"Supernumerary\" + ((event === null || event === void 0 ? void 0 : (_event_supernumerary2 = event.supernumerary) === null || _event_supernumerary2 === void 0 ? void 0 : _event_supernumerary2.title) ? \" - \" + (event === null || event === void 0 ? void 0 : event.supernumerary.title) : \"\");\n            case \"IncidentRecord\":\n                var _event_incidentRecord, _event_incidentRecord1;\n                return (((_event_incidentRecord = event.incidentRecord) === null || _event_incidentRecord === void 0 ? void 0 : _event_incidentRecord.startDate) ? dayjs__WEBPACK_IMPORTED_MODULE_21___default()(event.incidentRecord.startDate).format(\"HH:mm\") + \" - \" : \"\") + \"Incident Record\" + (((_event_incidentRecord1 = event.incidentRecord) === null || _event_incidentRecord1 === void 0 ? void 0 : _event_incidentRecord1.title) ? \" - \" + event.incidentRecord.title : \"\");\n            case \"InfringementNotice\":\n                var _event_infringementNotice, _event_infringementNotice1, _event_infringementNotice2;\n                return ((event === null || event === void 0 ? void 0 : (_event_infringementNotice = event.infringementNotice) === null || _event_infringementNotice === void 0 ? void 0 : _event_infringementNotice.time) ? dayjs__WEBPACK_IMPORTED_MODULE_21___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_21___default()().format(\"YYYY-MM-DD\"), \" \").concat(event === null || event === void 0 ? void 0 : event.infringementNotice.time)).format(\"HH:mm\") + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + ((event === null || event === void 0 ? void 0 : (_event_infringementNotice1 = event.infringementNotice) === null || _event_infringementNotice1 === void 0 ? void 0 : _event_infringementNotice1.geoLocation.title) ? \" - \" + (event === null || event === void 0 ? void 0 : (_event_infringementNotice2 = event.infringementNotice) === null || _event_infringementNotice2 === void 0 ? void 0 : _event_infringementNotice2.geoLocation.title) : \"\");\n            case \"CrewTraining\":\n                var _event_crewTraining, _event_crewTraining_geoLocation, _event_crewTraining1, _event_crewTraining_geoLocation1, _event_crewTraining2;\n                return (((_event_crewTraining = event.crewTraining) === null || _event_crewTraining === void 0 ? void 0 : _event_crewTraining.startTime) ? event.crewTraining.startTime + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_crewTraining1 = event.crewTraining) === null || _event_crewTraining1 === void 0 ? void 0 : (_event_crewTraining_geoLocation = _event_crewTraining1.geoLocation) === null || _event_crewTraining_geoLocation === void 0 ? void 0 : _event_crewTraining_geoLocation.title) ? \" - \" + ((_event_crewTraining2 = event.crewTraining) === null || _event_crewTraining2 === void 0 ? void 0 : (_event_crewTraining_geoLocation1 = _event_crewTraining2.geoLocation) === null || _event_crewTraining_geoLocation1 === void 0 ? void 0 : _event_crewTraining_geoLocation1.title) : \"\");\n            case \"VesselRescue\":\n                var _event_eventType_VesselRescue_mission, _event_eventType_VesselRescue, _event_eventType_VesselRescue_mission1, _event_eventType_VesselRescue1, _event_eventType_VesselRescue2;\n                return (((_event_eventType_VesselRescue = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue === void 0 ? void 0 : (_event_eventType_VesselRescue_mission = _event_eventType_VesselRescue.mission) === null || _event_eventType_VesselRescue_mission === void 0 ? void 0 : _event_eventType_VesselRescue_mission.completedAt) ? ((_event_eventType_VesselRescue1 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue1 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission1 = _event_eventType_VesselRescue1.mission) === null || _event_eventType_VesselRescue_mission1 === void 0 ? void 0 : _event_eventType_VesselRescue_mission1.completedAt) + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_eventType_VesselRescue2 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue2 === void 0 ? void 0 : _event_eventType_VesselRescue2.vesselName) ? \" - \" + event.eventType_VesselRescue.vesselName : \"\");\n            case \"HumanRescue\":\n                var _event_eventType_PersonRescue_mission, _event_eventType_PersonRescue, _event_eventType_PersonRescue_mission1, _event_eventType_PersonRescue1, _event_eventType_PersonRescue2;\n                return (((_event_eventType_PersonRescue = event.eventType_PersonRescue) === null || _event_eventType_PersonRescue === void 0 ? void 0 : (_event_eventType_PersonRescue_mission = _event_eventType_PersonRescue.mission) === null || _event_eventType_PersonRescue_mission === void 0 ? void 0 : _event_eventType_PersonRescue_mission.completedAt) ? ((_event_eventType_PersonRescue1 = event.eventType_PersonRescue) === null || _event_eventType_PersonRescue1 === void 0 ? void 0 : (_event_eventType_PersonRescue_mission1 = _event_eventType_PersonRescue1.mission) === null || _event_eventType_PersonRescue_mission1 === void 0 ? void 0 : _event_eventType_PersonRescue_mission1.completedAt) + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_eventType_PersonRescue2 = event.eventType_PersonRescue) === null || _event_eventType_PersonRescue2 === void 0 ? void 0 : _event_eventType_PersonRescue2.personName) ? \" - \" + event.eventType_PersonRescue.personName : \"\");\n            default:\n                return category.replace(/([a-z])([A-Z])/g, \"$1 $2\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_25__.H4, {\n                            children: \"ACTIVITIES\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 864,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_25__.P, {\n                            children: \"Record the events that happen during a voyage in this section.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 865,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                    lineNumber: 863,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                lineNumber: 862,\n                columnNumber: 13\n            }, this),\n            (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes = _currentTrip_tripEvents.nodes) === null || _currentTrip_tripEvents_nodes === void 0 ? void 0 : _currentTrip_tripEvents_nodes.length) > 0 || (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops1 = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops1 === void 0 ? void 0 : (_currentTrip_tripReport_Stops_nodes = _currentTrip_tripReport_Stops1.nodes) === null || _currentTrip_tripReport_Stops_nodes === void 0 ? void 0 : _currentTrip_tripReport_Stops_nodes.length) > 0 || !currentEvent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: ((currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes1 = _currentTrip_tripEvents1.nodes) === null || _currentTrip_tripEvents_nodes1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes1.length) > 0 || (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops2 = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops2 === void 0 ? void 0 : (_currentTrip_tripReport_Stops_nodes1 = _currentTrip_tripReport_Stops2.nodes) === null || _currentTrip_tripReport_Stops_nodes1 === void 0 ? void 0 : _currentTrip_tripReport_Stops_nodes1.length) > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.Accordion, {\n                    type: \"single\",\n                    collapsible: true,\n                    value: accordionValue,\n                    onValueChange: (value)=>{\n                        setAccordionValue(value);\n                        // If we're closing the accordion, reset the state\n                        if (value === \"\") {\n                            setSelectedRow(0);\n                            // setOpenEventModal(false)\n                            setCurrentEventType([]);\n                            setCurrentEvent(false);\n                            setCurrentStop(false);\n                        }\n                    },\n                    children: [\n                        currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.tripEvents.nodes.map((event, index)=>{\n                            var _event_eventType_PassengerDropFacility_type_replace_replace, _event_eventType_PassengerDropFacility_type_replace, _event_eventType_PassengerDropFacility_type, _event_eventType_PassengerDropFacility, _event_eventType_Tasking, _event_eventType_PassengerDropFacility1, _event_eventType_Tasking1, _event_eventType_Tasking2, _event_eventType_Tasking3, _event_eventType_RefuellingBunkering_fuelLog_nodes, _event_eventType_RefuellingBunkering_fuelLog, _event_eventType_RefuellingBunkering, _event_eventType_RefuellingBunkering_fuelLog1, _event_eventType_RefuellingBunkering1, _event_eventType_Tasking4, _event_eventType_Tasking5, _event_eventType_Tasking6, _event_eventType_Tasking7;\n                            // Generate event label and value outside the JSX\n                            const eventLabel = (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type = _event_eventType_PassengerDropFacility.type) === null || _event_eventType_PassengerDropFacility_type === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace = _event_eventType_PassengerDropFacility_type.replace(/([a-z])([A-Z])/g, \"$1 $2\")) === null || _event_eventType_PassengerDropFacility_type_replace === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace_replace = _event_eventType_PassengerDropFacility_type_replace.replace(\"Passenger Arrival\", \"Arrival\")) === null || _event_eventType_PassengerDropFacility_type_replace_replace === void 0 ? void 0 : _event_eventType_PassengerDropFacility_type_replace_replace.replace(\"Passenger Departure\", \"Departure\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type.replace(/([a-z])([A-Z])/g, \"$1 $2\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"EventSupernumerary\" ? \"Supernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory.replace(/([a-z])([A-Z])/g, \"$1 $2\");\n                            const eventValue = (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility1 = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility1 === void 0 ? void 0 : _event_eventType_PassengerDropFacility1.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Supernumerary\" ? \"EventSupernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory;\n                            // Generate event display text\n                            const eventDisplayText = getEventDisplayText(event);\n                            // Handle click on accordion item\n                            const handleAccordionItemClick = ()=>{\n                                // Toggle accordion state\n                                if (accordionValue === event.id.toString()) {\n                                    setAccordionValue(\"\");\n                                    setSelectedRow(0);\n                                    // setOpenEventModal(false)\n                                    setCurrentEventType([]);\n                                    setCurrentEvent(false);\n                                    setCurrentStop(false);\n                                } else {\n                                    setAccordionValue(event.id.toString());\n                                    setSelectedRow(event.id);\n                                    // setOpenEventModal(true)\n                                    setCurrentEventType({\n                                        label: eventLabel,\n                                        value: eventValue\n                                    });\n                                    setCurrentEvent(event);\n                                    setTripReport_Stops(false);\n                                    setDisplayDangerousGoodsPvpd(false);\n                                    setDisplayDangerousGoodsPvpdSailing(null);\n                                }\n                            };\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionItem, {\n                                value: event.id.toString(),\n                                className: (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.type) !== \"TaskingStartUnderway\" ? \"ml-[1.5rem]\" : \"\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionTrigger, {\n                                        onClick: handleAccordionItemClick,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center relative justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col inset-y-0 items-center justify-center absolute -left-[41px] sm:-left-[46px] w-5\",\n                                                    children: ((event === null || event === void 0 ? void 0 : event.eventCategory) !== \"Tasking\" || (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.type) == \"TaskingStartUnderway\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_29__.cn)(\"size-[11px] z-10 rounded-full\", currentEvent.id === event.id ? \"border border-primary bg-curious-blue-200\" : currentEvent.eventCategory === event.eventCategory ? \"border border-primary bg-curious-blue-200\" : \"border border-cool-wedgewood-200 bg-outer-space-50\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                        lineNumber: 1000,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 993,\n                                                    columnNumber: 53\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        eventDisplayText,\n                                                        (event === null || event === void 0 ? void 0 : event.eventCategory) === \"RefuellingBunkering\" && (event === null || event === void 0 ? void 0 : (_event_eventType_RefuellingBunkering = event.eventType_RefuellingBunkering) === null || _event_eventType_RefuellingBunkering === void 0 ? void 0 : (_event_eventType_RefuellingBunkering_fuelLog = _event_eventType_RefuellingBunkering.fuelLog) === null || _event_eventType_RefuellingBunkering_fuelLog === void 0 ? void 0 : (_event_eventType_RefuellingBunkering_fuelLog_nodes = _event_eventType_RefuellingBunkering_fuelLog.nodes) === null || _event_eventType_RefuellingBunkering_fuelLog_nodes === void 0 ? void 0 : _event_eventType_RefuellingBunkering_fuelLog_nodes.length) > 0 && getFuelTotals(event === null || event === void 0 ? void 0 : (_event_eventType_RefuellingBunkering1 = event.eventType_RefuellingBunkering) === null || _event_eventType_RefuellingBunkering1 === void 0 ? void 0 : (_event_eventType_RefuellingBunkering_fuelLog1 = _event_eventType_RefuellingBunkering1.fuelLog) === null || _event_eventType_RefuellingBunkering_fuelLog1 === void 0 ? void 0 : _event_eventType_RefuellingBunkering_fuelLog1.nodes)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1014,\n                                                    columnNumber: 53\n                                                }, this),\n                                                (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.type) === \"TaskingStartUnderway\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking5 = event.eventType_Tasking) === null || _event_eventType_Tasking5 === void 0 ? void 0 : _event_eventType_Tasking5.status) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"\".concat((event === null || event === void 0 ? void 0 : (_event_eventType_Tasking6 = event.eventType_Tasking) === null || _event_eventType_Tasking6 === void 0 ? void 0 : _event_eventType_Tasking6.status) === \"Open\" ? \"text-bright-turquoise-600\" : \"\", \" pr-2\"),\n                                                    children: event === null || event === void 0 ? void 0 : (_event_eventType_Tasking7 = event.eventType_Tasking) === null || _event_eventType_Tasking7 === void 0 ? void 0 : _event_eventType_Tasking7.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1036,\n                                                    columnNumber: 61\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                            lineNumber: 992,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 988,\n                                        columnNumber: 45\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionContent, {\n                                        className: \"pb-4\",\n                                        children: currentEventType && currentEvent && currentEvent.id === event.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                currentEventType.value === \"VesselRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_vessel_rescue__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1055,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"HumanRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_person_rescue__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1085,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"RestrictedVisibility\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_restricted_visibility__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity,\n                                                    members: crewMembers\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1115,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"BarCrossing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_bar_crossing__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    members: crewMembers,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1148,\n                                                    columnNumber: 65\n                                                }, this),\n                                                (currentEventType.value === \"PassengerArrival\" || currentEventType.value === \"PassengerDeparture\" || currentEventType.value === \"WaterTaxiService\" || currentEventType.value === \"ScheduledPassengerService\") && //TODO: update this form\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_drop_facility__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    inLogbook: true,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    type: currentEventType.value,\n                                                    logBookConfig: logBookConfig,\n                                                    previousDropEvent: previousDropEvent(currentEvent),\n                                                    vessel: vessel,\n                                                    locked: locked || !edit_tripActivity,\n                                                    fuelLogs: fuelLogs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1188,\n                                                    columnNumber: 65\n                                                }, this),\n                                                (currentEventType.value === \"TaskingStartUnderway\" || currentEventType.value === \"TaskingOnScene\" || currentEventType.value === \"TaskingOnTow\" || currentEventType.value === \"TaskingPaused\" || currentEventType.value === \"TaskingResumed\" || currentEventType.value === \"TaskingComplete\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_tasking__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    type: currentEventType.value,\n                                                    logBookConfig: logBookConfig,\n                                                    previousDropEvent: mainTaskingEvent(currentEvent),\n                                                    vessel: vessel,\n                                                    members: crewMembers,\n                                                    locked: locked || !edit_tripActivity,\n                                                    fuelLogs: fuelLogs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1244,\n                                                    columnNumber: 65\n                                                }, this),\n                                                permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: currentEventType.value === \"CrewTraining\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_crew_training_event__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        offline: offline,\n                                                        vesselId: +vesselID,\n                                                        trainingTypeId: 0,\n                                                        currentTrip: currentTrip,\n                                                        updateTripReport: updateTripReport,\n                                                        selectedEvent: currentEvent,\n                                                        tripReport: tripReport,\n                                                        closeModal: handleSetCurrentEventType,\n                                                        crewMembers: crewMembers,\n                                                        masterID: masterID,\n                                                        logBookConfig: logBookConfig,\n                                                        vessels: vessels,\n                                                        locked: locked || !edit_tripActivity,\n                                                        logBookStartDate: logBookStartDate\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                        lineNumber: 1298,\n                                                        columnNumber: 77\n                                                    }, this)\n                                                }, void 0, false),\n                                                currentEventType.value === \"EventSupernumerary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_supernumerary_event__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    offline: offline,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    tripReport: tripReport,\n                                                    selectedEvent: currentEvent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1348,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"RefuellingBunkering\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_refuelling_bunkering__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity,\n                                                    mainFuelLogs: fuelLogs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1378,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"TripUpdate\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_trip_update__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1414,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"PilotTransfer\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_pilot_transfer__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1441,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"InfringementNotice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_InfringementNotices__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    crewMembers: crewMembers,\n                                                    locked: locked || !edit_tripActivity,\n                                                    visibility: // selectedRow ===\n                                                    //     event.id &&\n                                                    currentEventType && currentEvent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1468,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"IncidentRecord\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_incident_record_incident_record_form__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    id: (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.incidentRecordID) || 0,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    inLogbook: true,\n                                                    selectedEvent: currentEvent,\n                                                    offline: offline,\n                                                    tripReport: tripReport\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1504,\n                                                    columnNumber: 65\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 1047,\n                                        columnNumber: 45\n                                    }, this)\n                                ]\n                            }, index + \"_events\", true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                lineNumber: 976,\n                                columnNumber: 41\n                            }, this);\n                        }),\n                        currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops3 = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops3 === void 0 ? void 0 : _currentTrip_tripReport_Stops3.nodes.map((event, index)=>{\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionItem, {\n                                value: \"stop_\".concat(event.id),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionTrigger, {\n                                        onClick: handleStopAccordionItemClick(event.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: getStopDisplayText(event)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                lineNumber: 1552,\n                                                columnNumber: 53\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                            lineNumber: 1551,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 1547,\n                                        columnNumber: 45\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionContent, {\n                                        className: \"pb-4\",\n                                        children: currentEventType && currentStop && currentStop.id === event.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: currentEventType.value === \"PassengerVehiclePickDrop\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_vehicle_pick_drop__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                offline: offline,\n                                                updateTripReport: updateTripReport,\n                                                currentTrip: currentTrip,\n                                                selectedEvent: currentStop,\n                                                tripReport: tripReport,\n                                                closeModal: handleSetCurrentEventType,\n                                                type: currentEventType.value,\n                                                logBookConfig: logBookConfig,\n                                                members: crewMembers,\n                                                locked: locked || !edit_tripActivity,\n                                                tripReport_Stops: tripReport_Stops,\n                                                setTripReport_Stops: setTripReport_Stops,\n                                                displayDangerousGoods: displayDangerousGoodsPvpd,\n                                                setDisplayDangerousGoods: setDisplayDangerousGoodsPvpd,\n                                                displayDangerousGoodsSailing: displayDangerousGoodsPvpdSailing,\n                                                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsPvpdSailing,\n                                                allPVPDDangerousGoods: allPVPDDangerousGoods,\n                                                setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                                                selectedDGR: selectedDGRPVPD,\n                                                setSelectedDGR: setSelectedDGRPVPD\n                                            }, \"pvpd-\".concat(event.id), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                lineNumber: 1567,\n                                                columnNumber: 65\n                                            }, this)\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 1559,\n                                        columnNumber: 45\n                                    }, this)\n                                ]\n                            }, index + \"_stops\", true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                lineNumber: 1544,\n                                columnNumber: 41\n                            }, this);\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                    lineNumber: 878,\n                    columnNumber: 25\n                }, this)\n            }, void 0, false) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-start gap-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                    position: \"left\",\n                    className: \"w-full\",\n                    label: \"Activity Type \",\n                    children: activityTypeOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_22__.Combobox, {\n                        id: \"task-assigned\",\n                        options: activityTypeOptions,\n                        value: currentEventType,\n                        onChange: handleEventChange,\n                        title: \"Activity Type\",\n                        placeholder: \"Activity Type\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                        lineNumber: 1650,\n                        columnNumber: 25\n                    }, this) : // Failsafe - in case the activity types are not loaded.\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"outline\",\n                            onClick: fetchActivityTypes,\n                            children: \"Refresh activity types\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1661,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                        lineNumber: 1660,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                    lineNumber: 1645,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                lineNumber: 1643,\n                columnNumber: 13\n            }, this),\n            currentEventType && !currentEvent && !currentStop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    currentEventType.value === \"VesselRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_vessel_rescue__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1695,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"HumanRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_person_rescue__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1709,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"RestrictedVisibility\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_restricted_visibility__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity,\n                            members: crewMembers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1723,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"BarCrossing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_bar_crossing__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            members: crewMembers,\n                            closeModal: handleSetCurrentEventType,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1738,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    (currentEventType.value === \"PassengerArrival\" || currentEventType.value === \"PassengerDeparture\" || currentEventType.value === \"WaterTaxiService\" || currentEventType.value === \"ScheduledPassengerService\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_drop_facility__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            type: currentEventType.value,\n                            logBookConfig: logBookConfig,\n                            inLogbook: true,\n                            previousDropEvent: previousDropEvent(currentEvent),\n                            vessel: vessel,\n                            locked: locked || !edit_tripActivity,\n                            fuelLogs: fuelLogs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1757,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    (currentEventType.value === \"TaskingStartUnderway\" || currentEventType.value === \"TaskingOnScene\" || currentEventType.value === \"TaskingOnTow\" || currentEventType.value === \"TaskingPaused\" || currentEventType.value === \"TaskingResumed\" || currentEventType.value === \"TaskingComplete\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_tasking__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            inLogbook: true,\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            type: currentEventType.value,\n                            logBookConfig: logBookConfig,\n                            previousDropEvent: mainTaskingEvent(currentEvent),\n                            vessel: vessel,\n                            members: crewMembers,\n                            locked: locked || !edit_tripActivity,\n                            fuelLogs: fuelLogs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1784,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: currentEventType.value === \"CrewTraining\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_crew_training_event__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                offline: offline,\n                                vesselId: +vesselID,\n                                trainingTypeId: 0,\n                                currentTrip: currentTrip,\n                                updateTripReport: updateTripReport,\n                                selectedEvent: currentEvent,\n                                tripReport: tripReport,\n                                closeModal: handleSetCurrentEventType,\n                                crewMembers: crewMembers,\n                                masterID: masterID,\n                                logBookConfig: logBookConfig,\n                                vessels: vessels,\n                                locked: locked || !edit_tripActivity,\n                                logBookStartDate: logBookStartDate\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                lineNumber: 1810,\n                                columnNumber: 41\n                            }, this)\n                        }, void 0, false)\n                    }, void 0, false),\n                    currentEventType.value === \"EventSupernumerary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_supernumerary_event__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            inLogbook: true,\n                            offline: offline,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity,\n                            closeModal: handleSetCurrentEventType,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            tripReport: tripReport,\n                            selectedEvent: currentEvent\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1836,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"PassengerVehiclePickDrop\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_vehicle_pick_drop__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentStop,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            type: currentEventType.value,\n                            logBookConfig: logBookConfig,\n                            members: crewMembers,\n                            locked: locked || !edit_tripActivity,\n                            tripReport_Stops: tripReport_Stops,\n                            setTripReport_Stops: setTripReport_Stops,\n                            displayDangerousGoods: displayDangerousGoodsPvpd,\n                            setDisplayDangerousGoods: setDisplayDangerousGoodsPvpd,\n                            displayDangerousGoodsSailing: displayDangerousGoodsPvpdSailing,\n                            setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsPvpdSailing,\n                            allPVPDDangerousGoods: allPVPDDangerousGoods,\n                            setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                            selectedDGR: selectedDGRPVPD,\n                            setSelectedDGR: setSelectedDGRPVPD\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1851,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"RefuellingBunkering\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_refuelling_bunkering__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity,\n                            mainFuelLogs: fuelLogs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1887,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"TripUpdate\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_trip_update__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1902,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"InfringementNotice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_InfringementNotices__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            crewMembers: crewMembers,\n                            locked: locked || !edit_tripActivity,\n                            visibility: currentEventType && !currentEvent && !currentStop\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1929,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"IncidentRecord\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_incident_record_incident_record_form__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                            id: (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.incidentRecordID) || 0,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            closeModal: handleSetCurrentEventType,\n                            inLogbook: true,\n                            selectedEvent: currentEvent,\n                            offline: offline,\n                            tripReport: tripReport\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1948,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false)\n                ]\n            }, void 0, true),\n            currentTrip.tripReportScheduleID > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radio_logs_schedule__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                open: displayRadioLogs,\n                setOpen: setDisplayRadioLogs,\n                currentTrip: currentTrip\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                lineNumber: 1963,\n                columnNumber: 17\n            }, this)\n        ]\n    }, lodash_uniqueId__WEBPACK_IMPORTED_MODULE_14___default()(), true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n        lineNumber: 861,\n        columnNumber: 9\n    }, this);\n}\n_s(Events, \"3rrNMu4zV6K2k4YQIASNWHgDV2c=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams\n    ];\n});\n_c = Events;\nvar _c;\n$RefreshReg$(_c, \"Events\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/events.tsx\n"));

/***/ })

});