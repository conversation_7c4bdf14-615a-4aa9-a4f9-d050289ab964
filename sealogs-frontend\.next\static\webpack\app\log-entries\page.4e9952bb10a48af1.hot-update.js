"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx":
/*!******************************************************************!*\
  !*** ./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PassengerVehiclePickDrop; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _pvpddgr__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../pvpddgr */ \"(app-pages-browser)/./src/app/ui/logbook/pvpddgr.tsx\");\n/* harmony import */ var _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsRecord */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsRecord.js\");\n/* harmony import */ var _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/tripReport_Stop */ \"(app-pages-browser)/./src/app/offline/models/tripReport_Stop.js\");\n/* harmony import */ var _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsChecklist */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsChecklist.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Create model instances outside component to prevent re-instantiation on every render\nconst dangerousGoodsRecordModel = new _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\nconst tripReport_StopModel = new _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\nconst dangerousGoodsChecklistModel = new _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\n\n\n\n\n\n\nfunction PassengerVehiclePickDrop(param) {\n    let { geoLocations, currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, type, logBookConfig, members, locked, tripReport_Stops, setTripReport_Stops, displayDangerousGoods = false, displayDangerousGoodsSailing, setDisplayDangerousGoods, setDisplayDangerousGoodsSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGR, setSelectedDGR, offline = false } = param;\n    _s();\n    const [arrTime, setArrTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [depTime, setDepTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [cargoOnOff, setCargoOnOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(selectedEvent);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [bufferDgr, setBufferDgr] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dgrChecklist, setDgrChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    // Local state for input values to prevent focus loss\n    const [localPaxOn, setLocalPaxOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localPaxOff, setLocalPaxOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOn, setLocalVehicleOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOff, setLocalVehicleOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // Memoize displayField results to prevent re-computation on every render\n    const displayFieldResults = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        const fieldMap = new Map();\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0) {\n            var _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n            (_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.forEach((field)=>{\n                if (field.status !== \"Off\") {\n                    fieldMap.set(field.fieldName, true);\n                }\n            });\n        }\n        return fieldMap;\n    }, [\n        logBookConfig\n    ]);\n    const displayField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((fieldName)=>{\n        return displayFieldResults.get(fieldName) || false;\n    }, [\n        displayFieldResults\n    ]);\n    // Stable onChange handlers that only update local state\n    const handlePaxOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOff(e.target.value);\n    }, []);\n    const handlePaxOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOn(e.target.value);\n    }, []);\n    const handleVehicleOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOn(e.target.value);\n    }, []);\n    const handleVehicleOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOff(e.target.value);\n    }, []);\n    // Stable onBlur handlers that update the main state\n    const handlePaxOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handlePaxOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    // Memoize currentEvent object to prevent inline object creation\n    const memoizedCurrentEvent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            geoLocationID: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID,\n            lat: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.lat,\n            long: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.long\n        }), [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.lat,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.long\n    ]);\n    // Memoize other callback functions\n    const handleLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        // If value is null or undefined, clear the location\n        if (!value) {\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: 0,\n                lat: null,\n                long: null\n            });\n            return;\n        }\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n            // If the value object has latitude and longitude, update currentLocation\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                setCurrentLocation({\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                });\n            }\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update currentLocation\n            setCurrentLocation({\n                latitude: value.latitude,\n                longitude: value.longitude\n            });\n        }\n    }, [\n        tripReport_Stops,\n        setTripReport_Stops,\n        setCurrentLocation\n    ]);\n    const handleArrTimeChange = (date)=>{\n        const formattedTime = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\");\n        setArrTime(formattedTime);\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            arriveTime: formattedTime,\n            arrTime: formattedTime\n        });\n    };\n    const handleDepTimeChange = (date)=>{\n        setDepTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            depTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"),\n            departTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\")\n        });\n    };\n    // Initialize local state from tripReport_Stops\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (tripReport_Stops) {\n            var _tripReport_Stops_paxOn, _tripReport_Stops_paxOff, _tripReport_Stops_vehicleOn, _tripReport_Stops_vehicleOff;\n            setLocalPaxOn(((_tripReport_Stops_paxOn = tripReport_Stops.paxOn) === null || _tripReport_Stops_paxOn === void 0 ? void 0 : _tripReport_Stops_paxOn.toString()) || \"\");\n            setLocalPaxOff(((_tripReport_Stops_paxOff = tripReport_Stops.paxOff) === null || _tripReport_Stops_paxOff === void 0 ? void 0 : _tripReport_Stops_paxOff.toString()) || \"\");\n            setLocalVehicleOn(((_tripReport_Stops_vehicleOn = tripReport_Stops.vehicleOn) === null || _tripReport_Stops_vehicleOn === void 0 ? void 0 : _tripReport_Stops_vehicleOn.toString()) || \"\");\n            setLocalVehicleOff(((_tripReport_Stops_vehicleOff = tripReport_Stops.vehicleOff) === null || _tripReport_Stops_vehicleOff === void 0 ? void 0 : _tripReport_Stops_vehicleOff.toString()) || \"\");\n        }\n    }, [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentTripReport_Stop(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n    }, [\n        selectedEvent\n    ]);\n    const offlineCreateDangerousGoodsChecklist = async ()=>{\n        // createDangerousGoodsChecklist\n        const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\n        await delay(2000);\n        const data = await dangerousGoodsChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__.generateUniqueId)()\n        });\n        setDgrChecklist(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (currentEvent) {\n            getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            setDgrChecklist(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.dangerousGoodsChecklist);\n        } else {\n            // Initialize default values for new records\n            if (!tripReport_Stops) {\n                setTripReport_Stops({\n                    paxOn: 0,\n                    paxOff: 0,\n                    vehicleOn: 0,\n                    vehicleOff: 0\n                });\n            }\n        }\n    }, [\n        currentEvent\n    ]);\n    const getCurrentTripReport_Stop = async (id)=>{\n        if (offline) {\n            // tripReport_Stop\n            const event = await tripReport_StopModel.getById(id);\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long\n                    });\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) && ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long)) {\n                        var _event_stopLocation4, _event_stopLocation5;\n                        setCurrentLocation({\n                            latitude: (_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat,\n                            longitude: (_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        setCurrentLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        } else {\n            tripReport_Stop({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const getBufferDgr = async (id)=>{\n        if (bufferDgr.length > 0) {\n            const dgr = bufferDgr.map((d)=>{\n                return +d.id;\n            });\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    ...dgr,\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            ...dgr,\n                            +id\n                        ]\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            +id\n                        ]\n                    }\n                });\n            }\n        }\n    };\n    const [getDgrList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetDangerousGoodsRecords, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setBufferDgr(data.readDangerousGoodsRecords.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error getting buffer dgr\", error);\n        }\n    });\n    const [tripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetTripReport_Stop, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripReport_Stop;\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setDisplayDangerousGoodsSailing(displayDangerousGoodsSailing !== null ? displayDangerousGoodsSailing : event === null || event === void 0 ? void 0 : event.designatedDangerousGoodsSailing);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long,\n                        designatedDangerousGoodsSailing: event.designatedDangerousGoodsSailing\n                    });\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) && ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long)) {\n                        var _event_stopLocation4, _event_stopLocation5;\n                        setCurrentLocation({\n                            latitude: (_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat,\n                            longitude: (_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        setCurrentLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    // Memoize locations array to prevent unnecessary re-creation\n    const memoizedLocations = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!geoLocations) return [];\n        return [\n            {\n                label: \"--- Add new location ---\",\n                value: \"newLocation\"\n            },\n            ...geoLocations.filter((location)=>location.title).map((location)=>({\n                    label: location.title,\n                    value: location.id,\n                    latitude: location.lat,\n                    longitude: location.long\n                }))\n        ];\n    }, [\n        geoLocations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setLocations(memoizedLocations);\n    }, [\n        memoizedLocations\n    ]);\n    const validateForm = ()=>{\n        // Validate stopLocationID\n        const stopLocationID = +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID);\n        if (!stopLocationID || stopLocationID <= 0) {\n            toast({\n                title: \"Error\",\n                description: \"Please select a trip stop location\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        // Validate arriveTime - check both arrTime and tripReport_Stops.arriveTime/arrTime\n        const arriveTimeValue = arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime);\n        // Use isEmpty but also check for false value since arrTime's initial state is false\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(arriveTimeValue) || arriveTimeValue === false) {\n            toast({\n                title: \"Error\",\n                description: \"Please enter an arrival time\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        return true;\n    };\n    const handleSave = async ()=>{\n        // Validate form before saving\n        if (!validateForm()) {\n            return;\n        }\n        // Use local state values for the most current input data\n        const paxOnValue = localPaxOn !== \"\" ? +localPaxOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn) || 0;\n        const paxOffValue = localPaxOff !== \"\" ? +localPaxOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff) || 0;\n        const vehicleOnValue = localVehicleOn !== \"\" ? +localVehicleOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn) || 0;\n        const vehicleOffValue = localVehicleOff !== \"\" ? +localVehicleOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff) || 0;\n        const variables = {\n            input: {\n                arriveTime: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime),\n                departTime: depTime ? depTime : tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.departTime,\n                paxJoined: paxOnValue,\n                paxDeparted: paxOffValue,\n                vehiclesJoined: isNaN(vehicleOnValue) ? 0 : vehicleOnValue,\n                vehiclesDeparted: isNaN(vehicleOffValue) ? 0 : vehicleOffValue,\n                stopLocationID: +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID),\n                otherCargo: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo,\n                comments: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments,\n                lat: currentLocation.latitude.toString(),\n                long: currentLocation.longitude.toString(),\n                dangerousGoodsChecklistID: +(dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.id),\n                designatedDangerousGoodsSailing: displayDangerousGoodsSailing\n            }\n        };\n        if (currentEvent) {\n            if (offline) {\n                // updateTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                    ...variables.input\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripReport_Stop({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            // Set default values for new records\n            variables.input.paxJoined = variables.input.paxJoined || 0;\n            variables.input.paxDeparted = variables.input.paxDeparted || 0;\n            if (offline) {\n                // createTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    ...variables.input,\n                    logBookEntrySectionID: currentTrip.id,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__.generateUniqueId)()\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                if (bufferDgr.length > 0) {\n                    Promise.all(bufferDgr.map(async (dgr)=>{\n                        // updateDangerousGoodsRecord\n                        const dgrData = await dangerousGoodsRecordModel.save({\n                            id: dgr.id,\n                            tripReport_StopID: data.id,\n                            type: dgr.type,\n                            comment: dgr.comment\n                        });\n                        // Clear any existing toasts\n                        if ((currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0) {\n                            await getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                        } else {\n                            if (dgrData) {\n                                await getBufferDgr(dgrData.id);\n                            }\n                        }\n                        // createDangerousGoodsChecklist\n                        const dgChecklistData = await dangerousGoodsChecklistModel.save({\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_13__.generateUniqueId)(),\n                            tripReport_StopID: data.id,\n                            vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                            bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                            twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                            fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                            noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                            spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                            fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                            dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                            loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                            msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                            anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                            safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                            vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                        });\n                        setDgrChecklist(dgChecklistData);\n                    }));\n                }\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                closeModal();\n            } else {\n                createTripReport_Stop({\n                    variables: {\n                        input: {\n                            ...variables.input,\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createDangerousGoodsChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_20__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateDangerousGoodsChecklist, {\n        onCompleted: (response)=>{\n            const data = response.createDangerousGoodsChecklist;\n            setDgrChecklist(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating dangerous goods\", error);\n        }\n    });\n    const [updateDangerousGoodsRecord] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_20__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateDangerousGoodsRecord, {\n        onCompleted: (response)=>{\n            const data = response.updateDangerousGoodsRecord;\n            // Process the response\n            (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0 ? getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) : getBufferDgr(data.id);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating dangerous goods record\", error);\n            toast({\n                title: \"Error\",\n                description: \"Error updating dangerous goods record\",\n                variant: \"destructive\"\n            });\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_20__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            if (bufferDgr.length > 0) {\n                bufferDgr.map((dgr)=>{\n                    updateDangerousGoodsRecord({\n                        variables: {\n                            input: {\n                                id: dgr.id,\n                                tripReport_StopID: data.id,\n                                type: dgr.type,\n                                comment: dgr.comment\n                            }\n                        }\n                    });\n                    createDangerousGoodsChecklist({\n                        variables: {\n                            input: {\n                                tripReport_StopID: data.id,\n                                vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                                bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                                twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                                fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                                noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                                spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                                fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                                dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                                loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                                msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                                anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                                safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                                vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                            }\n                        }\n                    });\n                });\n            }\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const [updateTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_20__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating passenger drop facility\", error);\n        }\n    });\n    var _tripReport_Stops_depTime;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_18__.P, {\n                className: \"max-w-[40rem] leading-loose\",\n                children: \"For recording trip stops where passengers, cargo and/or vehicles maybe getting on and off.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 806,\n                columnNumber: 13\n            }, this),\n            displayField(type + \"Location\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                label: \"Location of trip stop\",\n                htmlFor: \"trip-location\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    offline: offline,\n                    setCurrentLocation: setCurrentLocation,\n                    handleLocationChange: handleLocationChangeCallback,\n                    currentEvent: memoizedCurrentEvent\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 815,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 811,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Arrival\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                label: \"Arrival Time\",\n                htmlFor: \"arrival-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime) || \"\",\n                    handleTimeChange: handleArrTimeChange,\n                    timeID: \"arrival-time\",\n                    fieldName: \"Arrival Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 828,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 824,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Departure\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                label: \"Departure Time\",\n                htmlFor: \"departure-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: depTime ? depTime : (_tripReport_Stops_depTime = tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.depTime) !== null && _tripReport_Stops_depTime !== void 0 ? _tripReport_Stops_depTime : \"\",\n                    handleTimeChange: handleDepTimeChange,\n                    timeID: \"departure-time\",\n                    fieldName: \"Departure Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 846,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 842,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"PaxPickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                        label: \"Passengers off\",\n                        htmlFor: \"paxOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                            id: \"paxOff\",\n                            name: \"paxOff\",\n                            type: \"number\",\n                            value: localPaxOff,\n                            placeholder: \"Pax off\",\n                            min: \"0\",\n                            onChange: handlePaxOffChange,\n                            onBlur: handlePaxOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 863,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 859,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                        label: \"Passengers on\",\n                        htmlFor: \"paxOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                            id: \"paxOn\",\n                            name: \"paxOn\",\n                            type: \"number\",\n                            value: localPaxOn,\n                            placeholder: \"Pax on\",\n                            min: \"0\",\n                            onChange: handlePaxOnChange,\n                            onBlur: handlePaxOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 878,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 874,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 857,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"VehiclePickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                        label: \"Vehicles on\",\n                        htmlFor: \"vehicleOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                            id: \"vehicleOn\",\n                            name: \"vehicleOn\",\n                            type: \"number\",\n                            value: localVehicleOn,\n                            placeholder: \"Vehicles getting on\",\n                            min: \"0\",\n                            onChange: handleVehicleOnChange,\n                            onBlur: handleVehicleOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 898,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 894,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                        label: \"Vehicles off\",\n                        htmlFor: \"vehicleOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_14__.Input, {\n                            id: \"vehicleOff\",\n                            name: \"vehicleOff\",\n                            type: \"number\",\n                            value: localVehicleOff,\n                            placeholder: \"Vehicles getting off\",\n                            min: \"0\",\n                            onChange: handleVehicleOffChange,\n                            onBlur: handleVehicleOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 913,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 909,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 892,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"OtherCargo\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                label: \"Cargo (if any)\",\n                htmlFor: \"cargo-onOff\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__.Textarea, {\n                    id: \"cargo-onOff\",\n                    placeholder: \"Other cargo on and off\",\n                    value: cargoOnOff !== \"\" ? cargoOnOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo) || \"\",\n                    onChange: (e)=>{\n                        setCargoOnOff(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            otherCargo: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 931,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 927,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pvpddgr__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                offline: offline,\n                locked: locked,\n                currentTrip: currentTrip,\n                logBookConfig: logBookConfig,\n                selectedDGR: selectedDGR,\n                setSelectedDGR: setSelectedDGR,\n                members: members,\n                displayDangerousGoods: displayDangerousGoods,\n                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                allDangerousGoods: allPVPDDangerousGoods,\n                setAllDangerousGoods: setAllPVPDDangerousGoods,\n                currentEvent: tripEvent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 952,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_15__.Label, {\n                label: \"Comments\",\n                htmlFor: \"comments\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_16__.Textarea, {\n                    id: \"comments\",\n                    placeholder: \"Comments\",\n                    value: comments !== \"\" ? comments : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments) || \"\",\n                    onChange: (e)=>{\n                        setComments(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            comments: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 975,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 971,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 995,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_17__.Button, {\n                        variant: \"primary\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                        onClick: locked ? ()=>{} : handleSave,\n                        disabled: locked,\n                        children: selectedEvent ? \"Update\" : \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1001,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 994,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n        lineNumber: 805,\n        columnNumber: 9\n    }, this);\n}\n_s(PassengerVehiclePickDrop, \"bCKaPrc8yWVzEzxS0adpim+q8ko=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_19__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_20__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_20__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_20__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = PassengerVehiclePickDrop;\nvar _c;\n$RefreshReg$(_c, \"PassengerVehiclePickDrop\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx\n"));

/***/ })

});