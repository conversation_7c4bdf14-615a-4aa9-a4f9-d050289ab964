"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx":
/*!******************************************************************!*\
  !*** ./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PassengerVehiclePickDrop; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _pvpddgr__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../pvpddgr */ \"(app-pages-browser)/./src/app/ui/logbook/pvpddgr.tsx\");\n/* harmony import */ var _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsRecord */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsRecord.js\");\n/* harmony import */ var _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/tripReport_Stop */ \"(app-pages-browser)/./src/app/offline/models/tripReport_Stop.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsChecklist */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsChecklist.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Create model instances outside component to prevent re-instantiation on every render\nconst dangerousGoodsRecordModel = new _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\nconst tripReport_StopModel = new _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\nconst geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\nconst dangerousGoodsChecklistModel = new _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n\n\n\n\n\n\nfunction PassengerVehiclePickDrop(param) {\n    let { geoLocations, currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, type, logBookConfig, members, locked, tripReport_Stops, setTripReport_Stops, displayDangerousGoods = false, displayDangerousGoodsSailing, setDisplayDangerousGoods, setDisplayDangerousGoodsSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGR, setSelectedDGR, offline = false } = param;\n    _s();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [arrTime, setArrTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [depTime, setDepTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [cargoOnOff, setCargoOnOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(selectedEvent);\n    const [parentLocation, setParentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [bufferDgr, setBufferDgr] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dgrChecklist, setDgrChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    // Local state for input values to prevent focus loss\n    const [localPaxOn, setLocalPaxOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localPaxOff, setLocalPaxOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOn, setLocalVehicleOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOff, setLocalVehicleOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // Memoize displayField results to prevent re-computation on every render\n    const displayFieldResults = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        const fieldMap = new Map();\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0) {\n            var _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n            (_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.forEach((field)=>{\n                if (field.status !== \"Off\") {\n                    fieldMap.set(field.fieldName, true);\n                }\n            });\n        }\n        return fieldMap;\n    }, [\n        logBookConfig\n    ]);\n    const displayField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((fieldName)=>{\n        return displayFieldResults.get(fieldName) || false;\n    }, [\n        displayFieldResults\n    ]);\n    // Stable onChange handlers that only update local state\n    const handlePaxOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOff(e.target.value);\n    }, []);\n    const handlePaxOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOn(e.target.value);\n    }, []);\n    const handleVehicleOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOn(e.target.value);\n    }, []);\n    const handleVehicleOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOff(e.target.value);\n    }, []);\n    // Stable onBlur handlers that update the main state\n    const handlePaxOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handlePaxOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    // Memoize currentEvent object to prevent inline object creation\n    const memoizedCurrentEvent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            geoLocationID: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID,\n            lat: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.lat,\n            long: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.long\n        }), [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.lat,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.long\n    ]);\n    // Memoize other callback functions\n    const handleLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        // If value is null or undefined, clear the location\n        if (!value) {\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: 0,\n                lat: null,\n                long: null\n            });\n            return;\n        }\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n            // If the value object has latitude and longitude, update currentLocation\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                setCurrentLocation({\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                });\n            }\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update currentLocation\n            setCurrentLocation({\n                latitude: value.latitude,\n                longitude: value.longitude\n            });\n        }\n    }, [\n        tripReport_Stops,\n        setTripReport_Stops,\n        setCurrentLocation\n    ]);\n    const handleParentLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((selectedLocation)=>{\n        setParentLocation((selectedLocation === null || selectedLocation === void 0 ? void 0 : selectedLocation.value) || null);\n    }, [\n        setParentLocation\n    ]);\n    const handleArrTimeChange = (date)=>{\n        const formattedTime = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\");\n        setArrTime(formattedTime);\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            arriveTime: formattedTime,\n            arrTime: formattedTime\n        });\n    };\n    const handleDepTimeChange = (date)=>{\n        setDepTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            depTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"),\n            departTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\")\n        });\n    };\n    // Initialize local state from tripReport_Stops\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (tripReport_Stops) {\n            var _tripReport_Stops_paxOn, _tripReport_Stops_paxOff, _tripReport_Stops_vehicleOn, _tripReport_Stops_vehicleOff;\n            setLocalPaxOn(((_tripReport_Stops_paxOn = tripReport_Stops.paxOn) === null || _tripReport_Stops_paxOn === void 0 ? void 0 : _tripReport_Stops_paxOn.toString()) || \"\");\n            setLocalPaxOff(((_tripReport_Stops_paxOff = tripReport_Stops.paxOff) === null || _tripReport_Stops_paxOff === void 0 ? void 0 : _tripReport_Stops_paxOff.toString()) || \"\");\n            setLocalVehicleOn(((_tripReport_Stops_vehicleOn = tripReport_Stops.vehicleOn) === null || _tripReport_Stops_vehicleOn === void 0 ? void 0 : _tripReport_Stops_vehicleOn.toString()) || \"\");\n            setLocalVehicleOff(((_tripReport_Stops_vehicleOff = tripReport_Stops.vehicleOff) === null || _tripReport_Stops_vehicleOff === void 0 ? void 0 : _tripReport_Stops_vehicleOff.toString()) || \"\");\n        }\n    }, [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentTripReport_Stop(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n    }, [\n        selectedEvent\n    ]);\n    const offlineCreateDangerousGoodsChecklist = async ()=>{\n        // createDangerousGoodsChecklist\n        const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\n        await delay(2000);\n        const data = await dangerousGoodsChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n        });\n        setDgrChecklist(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (currentEvent) {\n            getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            setDgrChecklist(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.dangerousGoodsChecklist);\n        } else {\n            // Initialize default values for new records\n            if (!tripReport_Stops) {\n                setTripReport_Stops({\n                    paxOn: 0,\n                    paxOff: 0,\n                    vehicleOn: 0,\n                    vehicleOff: 0\n                });\n            }\n        }\n    }, [\n        currentEvent\n    ]);\n    const getCurrentTripReport_Stop = async (id)=>{\n        if (offline) {\n            // tripReport_Stop\n            const event = await tripReport_StopModel.getById(id);\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long\n                    });\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) && ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long)) {\n                        var _event_stopLocation4, _event_stopLocation5;\n                        setCurrentLocation({\n                            latitude: (_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat,\n                            longitude: (_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        setCurrentLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        } else {\n            tripReport_Stop({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const getBufferDgr = async (id)=>{\n        if (bufferDgr.length > 0) {\n            const dgr = bufferDgr.map((d)=>{\n                return +d.id;\n            });\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    ...dgr,\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            ...dgr,\n                            +id\n                        ]\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            +id\n                        ]\n                    }\n                });\n            }\n        }\n    };\n    const [getDgrList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_20__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetDangerousGoodsRecords, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setBufferDgr(data.readDangerousGoodsRecords.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error getting buffer dgr\", error);\n        }\n    });\n    const [tripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_20__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetTripReport_Stop, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripReport_Stop;\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setDisplayDangerousGoodsSailing(displayDangerousGoodsSailing !== null ? displayDangerousGoodsSailing : event === null || event === void 0 ? void 0 : event.designatedDangerousGoodsSailing);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long,\n                        designatedDangerousGoodsSailing: event.designatedDangerousGoodsSailing\n                    });\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) && ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long)) {\n                        var _event_stopLocation4, _event_stopLocation5;\n                        setCurrentLocation({\n                            latitude: (_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat,\n                            longitude: (_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        setCurrentLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    // Memoize locations array to prevent unnecessary re-creation\n    const memoizedLocations = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!geoLocations) return [];\n        return [\n            {\n                label: \"--- Add new location ---\",\n                value: \"newLocation\"\n            },\n            ...geoLocations.filter((location)=>location.title).map((location)=>({\n                    label: location.title,\n                    value: location.id,\n                    latitude: location.lat,\n                    longitude: location.long\n                }))\n        ];\n    }, [\n        geoLocations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setLocations(memoizedLocations);\n    }, [\n        memoizedLocations\n    ]);\n    const validateForm = ()=>{\n        // Validate stopLocationID\n        const stopLocationID = +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID);\n        if (!stopLocationID || stopLocationID <= 0) {\n            toast({\n                title: \"Error\",\n                description: \"Please select a trip stop location\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        // Validate arriveTime - check both arrTime and tripReport_Stops.arriveTime/arrTime\n        const arriveTimeValue = arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime);\n        // Use isEmpty but also check for false value since arrTime's initial state is false\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(arriveTimeValue) || arriveTimeValue === false) {\n            toast({\n                title: \"Error\",\n                description: \"Please enter an arrival time\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        return true;\n    };\n    const handleSave = async ()=>{\n        // Validate form before saving\n        if (!validateForm()) {\n            return;\n        }\n        // Use local state values for the most current input data\n        const paxOnValue = localPaxOn !== \"\" ? +localPaxOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn) || 0;\n        const paxOffValue = localPaxOff !== \"\" ? +localPaxOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff) || 0;\n        const vehicleOnValue = localVehicleOn !== \"\" ? +localVehicleOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn) || 0;\n        const vehicleOffValue = localVehicleOff !== \"\" ? +localVehicleOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff) || 0;\n        const variables = {\n            input: {\n                arriveTime: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime),\n                departTime: depTime ? depTime : tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.departTime,\n                paxJoined: paxOnValue,\n                paxDeparted: paxOffValue,\n                vehiclesJoined: isNaN(vehicleOnValue) ? 0 : vehicleOnValue,\n                vehiclesDeparted: isNaN(vehicleOffValue) ? 0 : vehicleOffValue,\n                stopLocationID: +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID),\n                otherCargo: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo,\n                comments: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments,\n                lat: currentLocation.latitude.toString(),\n                long: currentLocation.longitude.toString(),\n                dangerousGoodsChecklistID: +(dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.id),\n                designatedDangerousGoodsSailing: displayDangerousGoodsSailing\n            }\n        };\n        if (currentEvent) {\n            if (offline) {\n                // updateTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                    ...variables.input\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripReport_Stop({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            // Set default values for new records\n            variables.input.paxJoined = variables.input.paxJoined || 0;\n            variables.input.paxDeparted = variables.input.paxDeparted || 0;\n            if (offline) {\n                // createTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    ...variables.input,\n                    logBookEntrySectionID: currentTrip.id,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                if (bufferDgr.length > 0) {\n                    Promise.all(bufferDgr.map(async (dgr)=>{\n                        // updateDangerousGoodsRecord\n                        const dgrData = await dangerousGoodsRecordModel.save({\n                            id: dgr.id,\n                            tripReport_StopID: data.id,\n                            type: dgr.type,\n                            comment: dgr.comment\n                        });\n                        // Clear any existing toasts\n                        if ((currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0) {\n                            await getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                        } else {\n                            if (dgrData) {\n                                await getBufferDgr(dgrData.id);\n                            }\n                        }\n                        // createDangerousGoodsChecklist\n                        const dgChecklistData = await dangerousGoodsChecklistModel.save({\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                            tripReport_StopID: data.id,\n                            vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                            bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                            twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                            fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                            noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                            spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                            fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                            dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                            loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                            msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                            anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                            safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                            vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                        });\n                        setDgrChecklist(dgChecklistData);\n                    }));\n                }\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                closeModal();\n            } else {\n                createTripReport_Stop({\n                    variables: {\n                        input: {\n                            ...variables.input,\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createDangerousGoodsChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateDangerousGoodsChecklist, {\n        onCompleted: (response)=>{\n            const data = response.createDangerousGoodsChecklist;\n            setDgrChecklist(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating dangerous goods\", error);\n        }\n    });\n    const [updateDangerousGoodsRecord] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateDangerousGoodsRecord, {\n        onCompleted: (response)=>{\n            const data = response.updateDangerousGoodsRecord;\n            // Process the response\n            (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0 ? getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) : getBufferDgr(data.id);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating dangerous goods record\", error);\n            toast({\n                title: \"Error\",\n                description: \"Error updating dangerous goods record\",\n                variant: \"destructive\"\n            });\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            if (bufferDgr.length > 0) {\n                bufferDgr.map((dgr)=>{\n                    updateDangerousGoodsRecord({\n                        variables: {\n                            input: {\n                                id: dgr.id,\n                                tripReport_StopID: data.id,\n                                type: dgr.type,\n                                comment: dgr.comment\n                            }\n                        }\n                    });\n                    createDangerousGoodsChecklist({\n                        variables: {\n                            input: {\n                                tripReport_StopID: data.id,\n                                vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                                bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                                twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                                fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                                noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                                spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                                fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                                dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                                loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                                msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                                anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                                safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                                vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                            }\n                        }\n                    });\n                });\n            }\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const [updateTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating passenger drop facility\", error);\n        }\n    });\n    var _tripReport_Stops_depTime;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_19__.P, {\n                className: \"max-w-[40rem] leading-loose\",\n                children: \"For recording trip stops where passengers, cargo and/or vehicles maybe getting on and off.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 816,\n                columnNumber: 13\n            }, this),\n            displayField(type + \"Location\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Location of trip stop\",\n                htmlFor: \"trip-location\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    offline: offline,\n                    setCurrentLocation: setCurrentLocation,\n                    handleLocationChange: handleLocationChangeCallback,\n                    currentEvent: memoizedCurrentEvent\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 825,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 821,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Arrival\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Arrival Time\",\n                htmlFor: \"arrival-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime) || \"\",\n                    handleTimeChange: handleArrTimeChange,\n                    timeID: \"arrival-time\",\n                    fieldName: \"Arrival Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 838,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 834,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Departure\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Departure Time\",\n                htmlFor: \"departure-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: depTime ? depTime : (_tripReport_Stops_depTime = tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.depTime) !== null && _tripReport_Stops_depTime !== void 0 ? _tripReport_Stops_depTime : \"\",\n                    handleTimeChange: handleDepTimeChange,\n                    timeID: \"departure-time\",\n                    fieldName: \"Departure Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 856,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 852,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"PaxPickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers off\",\n                        htmlFor: \"paxOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOff\",\n                            name: \"paxOff\",\n                            type: \"number\",\n                            value: localPaxOff,\n                            placeholder: \"Pax off\",\n                            min: \"0\",\n                            onChange: handlePaxOffChange,\n                            onBlur: handlePaxOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 873,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 869,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers on\",\n                        htmlFor: \"paxOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOn\",\n                            name: \"paxOn\",\n                            type: \"number\",\n                            value: localPaxOn,\n                            placeholder: \"Pax on\",\n                            min: \"0\",\n                            onChange: handlePaxOnChange,\n                            onBlur: handlePaxOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 888,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 884,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 867,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"VehiclePickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles on\",\n                        htmlFor: \"vehicleOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOn\",\n                            name: \"vehicleOn\",\n                            type: \"number\",\n                            value: localVehicleOn,\n                            placeholder: \"Vehicles getting on\",\n                            min: \"0\",\n                            onChange: handleVehicleOnChange,\n                            onBlur: handleVehicleOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 908,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 904,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles off\",\n                        htmlFor: \"vehicleOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOff\",\n                            name: \"vehicleOff\",\n                            type: \"number\",\n                            value: localVehicleOff,\n                            placeholder: \"Vehicles getting off\",\n                            min: \"0\",\n                            onChange: handleVehicleOffChange,\n                            onBlur: handleVehicleOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 923,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 919,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 902,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"OtherCargo\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Cargo (if any)\",\n                htmlFor: \"cargo-onOff\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"cargo-onOff\",\n                    placeholder: \"Other cargo on and off\",\n                    value: cargoOnOff !== \"\" ? cargoOnOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo) || \"\",\n                    onChange: (e)=>{\n                        setCargoOnOff(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            otherCargo: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 941,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 937,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pvpddgr__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                offline: offline,\n                locked: locked,\n                currentTrip: currentTrip,\n                logBookConfig: logBookConfig,\n                selectedDGR: selectedDGR,\n                setSelectedDGR: setSelectedDGR,\n                members: members,\n                displayDangerousGoods: displayDangerousGoods,\n                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                allDangerousGoods: allPVPDDangerousGoods,\n                setAllDangerousGoods: setAllPVPDDangerousGoods,\n                currentEvent: tripEvent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 962,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Comments\",\n                htmlFor: \"comments\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"comments\",\n                    placeholder: \"Comments\",\n                    value: comments !== \"\" ? comments : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments) || \"\",\n                    onChange: (e)=>{\n                        setComments(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            comments: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 985,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 981,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1005,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"primary\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                        onClick: locked ? ()=>{} : handleSave,\n                        disabled: locked,\n                        children: selectedEvent ? \"Update\" : \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1011,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1004,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n        lineNumber: 815,\n        columnNumber: 9\n    }, this);\n}\n_s(PassengerVehiclePickDrop, \"qT8oSj4Kk8uWqWWA8y5yfrjKxgA=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_20__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_20__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation\n    ];\n});\n_c = PassengerVehiclePickDrop;\nvar _c;\n$RefreshReg$(_c, \"PassengerVehiclePickDrop\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx\n"));

/***/ })

});