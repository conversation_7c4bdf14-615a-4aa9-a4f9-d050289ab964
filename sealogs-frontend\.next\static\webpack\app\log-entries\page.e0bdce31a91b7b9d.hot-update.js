"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx":
/*!******************************************************************!*\
  !*** ./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PassengerVehiclePickDrop; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _pvpddgr__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../pvpddgr */ \"(app-pages-browser)/./src/app/ui/logbook/pvpddgr.tsx\");\n/* harmony import */ var _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsRecord */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsRecord.js\");\n/* harmony import */ var _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/tripReport_Stop */ \"(app-pages-browser)/./src/app/offline/models/tripReport_Stop.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsChecklist */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsChecklist.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Create model instances outside component to prevent re-instantiation on every render\nconst dangerousGoodsRecordModel = new _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\nconst tripReport_StopModel = new _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\nconst geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\nconst dangerousGoodsChecklistModel = new _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n\n\n\n\n\n\nfunction PassengerVehiclePickDrop(param) {\n    let { geoLocations, currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, type, logBookConfig, members, locked, tripReport_Stops, setTripReport_Stops, displayDangerousGoods = false, displayDangerousGoodsSailing, setDisplayDangerousGoods, setDisplayDangerousGoodsSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGR, setSelectedDGR, offline = false } = param;\n    _s();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [arrTime, setArrTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [depTime, setDepTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [cargoOnOff, setCargoOnOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(selectedEvent);\n    const [parentLocation, setParentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [bufferDgr, setBufferDgr] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dgrChecklist, setDgrChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [openNewLocationDialog, setOpenNewLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Local state for input values to prevent focus loss\n    const [localPaxOn, setLocalPaxOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localPaxOff, setLocalPaxOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOn, setLocalVehicleOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOff, setLocalVehicleOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // Memoize displayField results to prevent re-computation on every render\n    const displayFieldResults = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        const fieldMap = new Map();\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0) {\n            var _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n            (_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.forEach((field)=>{\n                if (field.status !== \"Off\") {\n                    fieldMap.set(field.fieldName, true);\n                }\n            });\n        }\n        return fieldMap;\n    }, [\n        logBookConfig\n    ]);\n    const displayField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((fieldName)=>{\n        return displayFieldResults.get(fieldName) || false;\n    }, [\n        displayFieldResults\n    ]);\n    // Stable onChange handlers that only update local state\n    const handlePaxOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOff(e.target.value);\n    }, []);\n    const handlePaxOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOn(e.target.value);\n    }, []);\n    const handleVehicleOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOn(e.target.value);\n    }, []);\n    const handleVehicleOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOff(e.target.value);\n    }, []);\n    // Stable onBlur handlers that update the main state\n    const handlePaxOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handlePaxOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    // Memoize currentEvent object to prevent inline object creation\n    const memoizedCurrentEvent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            geoLocationID: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID,\n            lat: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.lat,\n            long: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.long\n        }), [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.lat,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.long\n    ]);\n    // Memoize other callback functions\n    const handleLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        // If value is null or undefined, clear the location\n        if (!value) {\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: 0,\n                lat: null,\n                long: null\n            });\n            return;\n        }\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n            // If the value object has latitude and longitude, update currentLocation\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                setCurrentLocation({\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                });\n            }\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update currentLocation\n            setCurrentLocation({\n                latitude: value.latitude,\n                longitude: value.longitude\n            });\n        }\n    }, [\n        tripReport_Stops,\n        setTripReport_Stops,\n        setCurrentLocation\n    ]);\n    const handleParentLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((selectedLocation)=>{\n        setParentLocation((selectedLocation === null || selectedLocation === void 0 ? void 0 : selectedLocation.value) || null);\n    }, [\n        setParentLocation\n    ]);\n    const handleArrTimeChange = (date)=>{\n        const formattedTime = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\");\n        setArrTime(formattedTime);\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            arriveTime: formattedTime,\n            arrTime: formattedTime\n        });\n    };\n    const handleDepTimeChange = (date)=>{\n        setDepTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            depTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"),\n            departTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\")\n        });\n    };\n    // Initialize local state from tripReport_Stops\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (tripReport_Stops) {\n            var _tripReport_Stops_paxOn, _tripReport_Stops_paxOff, _tripReport_Stops_vehicleOn, _tripReport_Stops_vehicleOff;\n            setLocalPaxOn(((_tripReport_Stops_paxOn = tripReport_Stops.paxOn) === null || _tripReport_Stops_paxOn === void 0 ? void 0 : _tripReport_Stops_paxOn.toString()) || \"\");\n            setLocalPaxOff(((_tripReport_Stops_paxOff = tripReport_Stops.paxOff) === null || _tripReport_Stops_paxOff === void 0 ? void 0 : _tripReport_Stops_paxOff.toString()) || \"\");\n            setLocalVehicleOn(((_tripReport_Stops_vehicleOn = tripReport_Stops.vehicleOn) === null || _tripReport_Stops_vehicleOn === void 0 ? void 0 : _tripReport_Stops_vehicleOn.toString()) || \"\");\n            setLocalVehicleOff(((_tripReport_Stops_vehicleOff = tripReport_Stops.vehicleOff) === null || _tripReport_Stops_vehicleOff === void 0 ? void 0 : _tripReport_Stops_vehicleOff.toString()) || \"\");\n        }\n    }, [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentTripReport_Stop(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n    }, [\n        selectedEvent\n    ]);\n    const offlineCreateDangerousGoodsChecklist = async ()=>{\n        // createDangerousGoodsChecklist\n        const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\n        await delay(2000);\n        const data = await dangerousGoodsChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n        });\n        setDgrChecklist(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (currentEvent) {\n            getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            setDgrChecklist(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.dangerousGoodsChecklist);\n        } else {\n            // Initialize default values for new records\n            if (!tripReport_Stops) {\n                setTripReport_Stops({\n                    paxOn: 0,\n                    paxOff: 0,\n                    vehicleOn: 0,\n                    vehicleOff: 0\n                });\n            }\n        }\n    }, [\n        currentEvent\n    ]);\n    const getCurrentTripReport_Stop = async (id)=>{\n        if (offline) {\n            // tripReport_Stop\n            const event = await tripReport_StopModel.getById(id);\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long\n                    });\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) && ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long)) {\n                        var _event_stopLocation4, _event_stopLocation5;\n                        setCurrentLocation({\n                            latitude: (_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat,\n                            longitude: (_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        setCurrentLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        } else {\n            tripReport_Stop({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const getBufferDgr = async (id)=>{\n        if (bufferDgr.length > 0) {\n            const dgr = bufferDgr.map((d)=>{\n                return +d.id;\n            });\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    ...dgr,\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            ...dgr,\n                            +id\n                        ]\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            +id\n                        ]\n                    }\n                });\n            }\n        }\n    };\n    const [getDgrList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_20__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetDangerousGoodsRecords, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setBufferDgr(data.readDangerousGoodsRecords.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error getting buffer dgr\", error);\n        }\n    });\n    const [tripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_20__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetTripReport_Stop, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripReport_Stop;\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setDisplayDangerousGoodsSailing(displayDangerousGoodsSailing !== null ? displayDangerousGoodsSailing : event === null || event === void 0 ? void 0 : event.designatedDangerousGoodsSailing);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long,\n                        designatedDangerousGoodsSailing: event.designatedDangerousGoodsSailing\n                    });\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) && ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long)) {\n                        var _event_stopLocation4, _event_stopLocation5;\n                        setCurrentLocation({\n                            latitude: (_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat,\n                            longitude: (_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        setCurrentLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    // Memoize locations array to prevent unnecessary re-creation\n    const memoizedLocations = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!geoLocations) return [];\n        return [\n            {\n                label: \"--- Add new location ---\",\n                value: \"newLocation\"\n            },\n            ...geoLocations.filter((location)=>location.title).map((location)=>({\n                    label: location.title,\n                    value: location.id,\n                    latitude: location.lat,\n                    longitude: location.long\n                }))\n        ];\n    }, [\n        geoLocations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setLocations(memoizedLocations);\n    }, [\n        memoizedLocations\n    ]);\n    const validateForm = ()=>{\n        // Validate stopLocationID\n        const stopLocationID = +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID);\n        if (!stopLocationID || stopLocationID <= 0) {\n            toast({\n                title: \"Error\",\n                description: \"Please select a trip stop location\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        // Validate arriveTime - check both arrTime and tripReport_Stops.arriveTime/arrTime\n        const arriveTimeValue = arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime);\n        // Use isEmpty but also check for false value since arrTime's initial state is false\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(arriveTimeValue) || arriveTimeValue === false) {\n            toast({\n                title: \"Error\",\n                description: \"Please enter an arrival time\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        return true;\n    };\n    const handleSave = async ()=>{\n        // Validate form before saving\n        if (!validateForm()) {\n            return;\n        }\n        // Use local state values for the most current input data\n        const paxOnValue = localPaxOn !== \"\" ? +localPaxOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn) || 0;\n        const paxOffValue = localPaxOff !== \"\" ? +localPaxOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff) || 0;\n        const vehicleOnValue = localVehicleOn !== \"\" ? +localVehicleOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn) || 0;\n        const vehicleOffValue = localVehicleOff !== \"\" ? +localVehicleOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff) || 0;\n        const variables = {\n            input: {\n                arriveTime: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime),\n                departTime: depTime ? depTime : tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.departTime,\n                paxJoined: paxOnValue,\n                paxDeparted: paxOffValue,\n                vehiclesJoined: isNaN(vehicleOnValue) ? 0 : vehicleOnValue,\n                vehiclesDeparted: isNaN(vehicleOffValue) ? 0 : vehicleOffValue,\n                stopLocationID: +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID),\n                otherCargo: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo,\n                comments: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments,\n                lat: currentLocation.latitude.toString(),\n                long: currentLocation.longitude.toString(),\n                dangerousGoodsChecklistID: +(dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.id),\n                designatedDangerousGoodsSailing: displayDangerousGoodsSailing\n            }\n        };\n        if (currentEvent) {\n            if (offline) {\n                // updateTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                    ...variables.input\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripReport_Stop({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            // Set default values for new records\n            variables.input.paxJoined = variables.input.paxJoined || 0;\n            variables.input.paxDeparted = variables.input.paxDeparted || 0;\n            if (offline) {\n                // createTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    ...variables.input,\n                    logBookEntrySectionID: currentTrip.id,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                if (bufferDgr.length > 0) {\n                    Promise.all(bufferDgr.map(async (dgr)=>{\n                        // updateDangerousGoodsRecord\n                        const dgrData = await dangerousGoodsRecordModel.save({\n                            id: dgr.id,\n                            tripReport_StopID: data.id,\n                            type: dgr.type,\n                            comment: dgr.comment\n                        });\n                        // Clear any existing toasts\n                        if ((currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0) {\n                            await getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                        } else {\n                            if (dgrData) {\n                                await getBufferDgr(dgrData.id);\n                            }\n                        }\n                        // createDangerousGoodsChecklist\n                        const dgChecklistData = await dangerousGoodsChecklistModel.save({\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                            tripReport_StopID: data.id,\n                            vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                            bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                            twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                            fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                            noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                            spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                            fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                            dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                            loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                            msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                            anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                            safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                            vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                        });\n                        setDgrChecklist(dgChecklistData);\n                    }));\n                }\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                closeModal();\n            } else {\n                createTripReport_Stop({\n                    variables: {\n                        input: {\n                            ...variables.input,\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createDangerousGoodsChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateDangerousGoodsChecklist, {\n        onCompleted: (response)=>{\n            const data = response.createDangerousGoodsChecklist;\n            setDgrChecklist(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating dangerous goods\", error);\n        }\n    });\n    const [updateDangerousGoodsRecord] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateDangerousGoodsRecord, {\n        onCompleted: (response)=>{\n            const data = response.updateDangerousGoodsRecord;\n            // Process the response\n            (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0 ? getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) : getBufferDgr(data.id);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating dangerous goods record\", error);\n            toast({\n                title: \"Error\",\n                description: \"Error updating dangerous goods record\",\n                variant: \"destructive\"\n            });\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            if (bufferDgr.length > 0) {\n                bufferDgr.map((dgr)=>{\n                    updateDangerousGoodsRecord({\n                        variables: {\n                            input: {\n                                id: dgr.id,\n                                tripReport_StopID: data.id,\n                                type: dgr.type,\n                                comment: dgr.comment\n                            }\n                        }\n                    });\n                    createDangerousGoodsChecklist({\n                        variables: {\n                            input: {\n                                tripReport_StopID: data.id,\n                                vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                                bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                                twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                                fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                                noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                                spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                                fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                                dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                                loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                                msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                                anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                                safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                                vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                            }\n                        }\n                    });\n                });\n            }\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const [updateTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating passenger drop facility\", error);\n        }\n    });\n    const handleCreateNewLocation = async ()=>{\n        const title = document.getElementById(\"new-location-title\");\n        const latitude = document.getElementById(\"new-location-latitude\");\n        const longitude = document.getElementById(\"new-location-longitude\");\n        if (title && latitude && longitude) {\n            if (offline) {\n                // createGeoLocation\n                const data = await geoLocationModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    title: title.value,\n                    lat: +latitude.value,\n                    long: +longitude.value,\n                    parentID: parentLocation\n                });\n                setLocations([\n                    ...locations,\n                    {\n                        label: data.title,\n                        value: data.id,\n                        latitude: data.lat,\n                        longitude: data.long\n                    }\n                ]);\n                setTripReport_Stops({\n                    ...tripReport_Stops,\n                    geoLocationID: data.id\n                });\n                setOpenNewLocationDialog(false);\n            } else {\n                createGeoLocation({\n                    variables: {\n                        input: {\n                            title: title.value,\n                            lat: +latitude.value,\n                            long: +longitude.value,\n                            parentID: parentLocation\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createGeoLocation] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_GEO_LOCATION, {\n        onCompleted: (response)=>{\n            const data = response.createGeoLocation;\n            setLocations([\n                ...locations,\n                {\n                    label: data.title,\n                    value: data.id,\n                    latitude: data.lat,\n                    longitude: data.long\n                }\n            ]);\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: data.id\n            });\n            setOpenNewLocationDialog(false);\n        },\n        onError: (error)=>{\n            toast({\n                title: \"Error\",\n                description: \"Error creating GeoLocation: \" + error.message,\n                variant: \"destructive\"\n            });\n            setOpenNewLocationDialog(false);\n            console.error(\"Error creating new location\", error);\n        }\n    });\n    var _tripReport_Stops_depTime;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_19__.P, {\n                className: \"max-w-[40rem] leading-loose\",\n                children: \"For recording trip stops where passengers, cargo and/or vehicles maybe getting on and off.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 900,\n                columnNumber: 13\n            }, this),\n            displayField(type + \"Location\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Location of trip stop\",\n                htmlFor: \"trip-location\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    offline: offline,\n                    setCurrentLocation: setCurrentLocation,\n                    handleLocationChange: handleLocationChangeCallback,\n                    currentEvent: memoizedCurrentEvent\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 909,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 905,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Arrival\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Arrival Time\",\n                htmlFor: \"arrival-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime) || \"\",\n                    handleTimeChange: handleArrTimeChange,\n                    timeID: \"arrival-time\",\n                    fieldName: \"Arrival Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 922,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 918,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Departure\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Departure Time\",\n                htmlFor: \"departure-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: depTime ? depTime : (_tripReport_Stops_depTime = tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.depTime) !== null && _tripReport_Stops_depTime !== void 0 ? _tripReport_Stops_depTime : \"\",\n                    handleTimeChange: handleDepTimeChange,\n                    timeID: \"departure-time\",\n                    fieldName: \"Departure Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 940,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 936,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"PaxPickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers off\",\n                        htmlFor: \"paxOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOff\",\n                            name: \"paxOff\",\n                            type: \"number\",\n                            value: localPaxOff,\n                            placeholder: \"Pax off\",\n                            min: \"0\",\n                            onChange: handlePaxOffChange,\n                            onBlur: handlePaxOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 957,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 953,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers on\",\n                        htmlFor: \"paxOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOn\",\n                            name: \"paxOn\",\n                            type: \"number\",\n                            value: localPaxOn,\n                            placeholder: \"Pax on\",\n                            min: \"0\",\n                            onChange: handlePaxOnChange,\n                            onBlur: handlePaxOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 972,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 968,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 951,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"VehiclePickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles on\",\n                        htmlFor: \"vehicleOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOn\",\n                            name: \"vehicleOn\",\n                            type: \"number\",\n                            value: localVehicleOn,\n                            placeholder: \"Vehicles getting on\",\n                            min: \"0\",\n                            onChange: handleVehicleOnChange,\n                            onBlur: handleVehicleOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 992,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 988,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles off\",\n                        htmlFor: \"vehicleOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOff\",\n                            name: \"vehicleOff\",\n                            type: \"number\",\n                            value: localVehicleOff,\n                            placeholder: \"Vehicles getting off\",\n                            min: \"0\",\n                            onChange: handleVehicleOffChange,\n                            onBlur: handleVehicleOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1007,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1003,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 986,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"OtherCargo\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Cargo (if any)\",\n                htmlFor: \"cargo-onOff\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"cargo-onOff\",\n                    placeholder: \"Other cargo on and off\",\n                    value: cargoOnOff !== \"\" ? cargoOnOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo) || \"\",\n                    onChange: (e)=>{\n                        setCargoOnOff(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            otherCargo: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1025,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1021,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pvpddgr__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                offline: offline,\n                locked: locked,\n                currentTrip: currentTrip,\n                logBookConfig: logBookConfig,\n                selectedDGR: selectedDGR,\n                setSelectedDGR: setSelectedDGR,\n                members: members,\n                displayDangerousGoods: displayDangerousGoods,\n                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                allDangerousGoods: allPVPDDangerousGoods,\n                setAllDangerousGoods: setAllPVPDDangerousGoods,\n                currentEvent: tripEvent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1046,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Comments\",\n                htmlFor: \"comments\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"comments\",\n                    placeholder: \"Comments\",\n                    value: comments !== \"\" ? comments : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments) || \"\",\n                    onChange: (e)=>{\n                        setComments(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            comments: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1069,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1065,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1089,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"primary\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                        onClick: locked ? ()=>{} : handleSave,\n                        disabled: locked,\n                        children: selectedEvent ? \"Update\" : \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1095,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1088,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n        lineNumber: 899,\n        columnNumber: 9\n    }, this);\n}\n_s(PassengerVehiclePickDrop, \"FcV64c9PeXf8lTDZUaoOswTKPBM=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_20__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_20__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useMutation\n    ];\n});\n_c = PassengerVehiclePickDrop;\nvar _c;\n$RefreshReg$(_c, \"PassengerVehiclePickDrop\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx\n"));

/***/ })

});