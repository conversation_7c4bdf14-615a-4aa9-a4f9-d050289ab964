'use client'
import dayjs from 'dayjs'
import React, { useEffect, useState, useMemo, useCallback } from 'react'
import { isEmpty } from 'lodash'

import {
    CreateTripReport_Stop,
    UpdateTripReport_Stop,
    CREATE_GEO_LOCATION,
    CreateDangerousGoodsChecklist,
    UpdateDangerousGoodsRecord,
} from '@/app/lib/graphQL/mutation'
import {
    GetDangerousGoodsRecords,
    GetTripReport_Stop,
} from '@/app/lib/graphQL/query'
import { useLazyQuery, useMutation } from '@apollo/client'
import { useToast } from '@/hooks/use-toast'

import TimeField from '../components/time'
import LocationField from '../components/location'
import PVPDDGR from '../pvpddgr'
import DangerousGoodsRecordModel from '@/app/offline/models/dangerousGoodsRecord'
import TripReport_StopModel from '@/app/offline/models/tripReport_Stop'
import GeoLocationModel from '@/app/offline/models/geoLocation'
import DangerousGoodsChecklistModel from '@/app/offline/models/dangerousGoodsChecklist'
import { generateUniqueId } from '@/app/offline/helpers/functions'

// Create model instances outside component to prevent re-instantiation on every render
const dangerousGoodsRecordModel = new DangerousGoodsRecordModel()
const tripReport_StopModel = new TripReport_StopModel()
const geoLocationModel = new GeoLocationModel()
const dangerousGoodsChecklistModel = new DangerousGoodsChecklistModel()
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Check, ArrowLeft } from 'lucide-react'
import { P } from '@/components/ui'

export default function PassengerVehiclePickDrop({
    currentTrip = false,
    updateTripReport,
    selectedEvent = false,
    tripReport,
    closeModal,
    type,
    logBookConfig,
    members,
    locked,
    tripReport_Stops,
    setTripReport_Stops,
    displayDangerousGoods = false,
    displayDangerousGoodsSailing,
    setDisplayDangerousGoods,
    setDisplayDangerousGoodsSailing,
    allPVPDDangerousGoods,
    setAllPVPDDangerousGoods,
    selectedDGR,
    setSelectedDGR,
    offline = false,
}: {
    currentTrip: any
    updateTripReport: any
    selectedEvent: any
    tripReport: any
    closeModal: any
    type: any
    logBookConfig: any
    members: any
    locked: any
    tripReport_Stops: any
    setTripReport_Stops: any
    displayDangerousGoods: boolean
    displayDangerousGoodsSailing: any
    setDisplayDangerousGoods: any
    setDisplayDangerousGoodsSailing: any
    allPVPDDangerousGoods: any
    setAllPVPDDangerousGoods: any
    selectedDGR: any
    setSelectedDGR: any
    offline?: boolean
}) {
    const [arrTime, setArrTime] = useState<any>(false)
    const [depTime, setDepTime] = useState<any>(false)
    const [cargoOnOff, setCargoOnOff] = useState<any>('')
    const [currentEvent, setCurrentEvent] = useState<any>(selectedEvent)
    const [comments, setComments] = useState<any>('')
    const [bufferDgr, setBufferDgr] = useState<any>([])
    const [dgrChecklist, setDgrChecklist] = useState<any>([])
    const [tripEvent, setTripEvent] = useState<any>(false)
    const [currentLocation, setCurrentLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })

    // Local state for input values to prevent focus loss
    const [localPaxOn, setLocalPaxOn] = useState<string>('')
    const [localPaxOff, setLocalPaxOff] = useState<string>('')
    const [localVehicleOn, setLocalVehicleOn] = useState<string>('')
    const [localVehicleOff, setLocalVehicleOff] = useState<string>('')

    const { toast } = useToast()

    // Memoize displayField results to prevent re-computation on every render
    const displayFieldResults = useMemo(() => {
        const eventTypesConfig =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'EventType_LogBookComponent',
            )

        const fieldMap = new Map()

        if (eventTypesConfig?.length > 0) {
            eventTypesConfig[0]?.customisedComponentFields?.nodes.forEach(
                (field: any) => {
                    if (field.status !== 'Off') {
                        fieldMap.set(field.fieldName, true)
                    }
                },
            )
        }

        return fieldMap
    }, [logBookConfig])

    const displayField = useCallback(
        (fieldName: string) => {
            return displayFieldResults.get(fieldName) || false
        },
        [displayFieldResults],
    )

    // Stable onChange handlers that only update local state
    const handlePaxOffChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            setLocalPaxOff(e.target.value)
        },
        [],
    )

    const handlePaxOnChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            setLocalPaxOn(e.target.value)
        },
        [],
    )

    const handleVehicleOnChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            setLocalVehicleOn(e.target.value)
        },
        [],
    )

    const handleVehicleOffChange = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            setLocalVehicleOff(e.target.value)
        },
        [],
    )

    // Stable onBlur handlers that update the main state
    const handlePaxOffBlur = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            setTripReport_Stops((prev: any) => ({
                ...prev,
                paxOff: e.target.value === '' ? 0 : +e.target.value,
            }))
        },
        [setTripReport_Stops],
    )

    const handlePaxOnBlur = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            setTripReport_Stops((prev: any) => ({
                ...prev,
                paxOn: e.target.value === '' ? 0 : +e.target.value,
            }))
        },
        [setTripReport_Stops],
    )

    const handleVehicleOnBlur = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            setTripReport_Stops((prev: any) => ({
                ...prev,
                vehicleOn: e.target.value === '' ? 0 : +e.target.value,
            }))
        },
        [setTripReport_Stops],
    )

    const handleVehicleOffBlur = useCallback(
        (e: React.ChangeEvent<HTMLInputElement>) => {
            setTripReport_Stops((prev: any) => ({
                ...prev,
                vehicleOff: e.target.value === '' ? 0 : +e.target.value,
            }))
        },
        [setTripReport_Stops],
    )

    // Memoize currentEvent object to prevent inline object creation
    const memoizedCurrentEvent = useMemo(
        () => ({
            geoLocationID: tripReport_Stops?.geoLocationID,
            lat: tripReport_Stops?.lat,
            long: tripReport_Stops?.long,
        }),
        [
            tripReport_Stops?.geoLocationID,
            tripReport_Stops?.lat,
            tripReport_Stops?.long,
        ],
    )

    // Memoize other callback functions
    const handleLocationChangeCallback = useCallback(
        (value: any) => {
            // If value is null or undefined, clear the location
            if (!value) {
                setTripReport_Stops({
                    ...tripReport_Stops,
                    geoLocationID: 0,
                    lat: null,
                    long: null,
                })
                return
            }

            // Check if the value is from dropdown selection (has 'value' property)
            if (value.value) {
                // Handle location selected from dropdown
                setTripReport_Stops({
                    ...tripReport_Stops,
                    geoLocationID: +value.value,
                    lat: null,
                    long: null,
                })

                // If the value object has latitude and longitude, update currentLocation
                if (
                    value.latitude !== undefined &&
                    value.longitude !== undefined
                ) {
                    setCurrentLocation({
                        latitude: value.latitude,
                        longitude: value.longitude,
                    })
                }
            } else if (
                value.latitude !== undefined &&
                value.longitude !== undefined
            ) {
                // Handle direct coordinates input
                setTripReport_Stops({
                    ...tripReport_Stops,
                    geoLocationID: 0, // Reset geoLocationID when using direct coordinates
                    lat: value.latitude,
                    long: value.longitude,
                })

                // Update currentLocation
                setCurrentLocation({
                    latitude: value.latitude,
                    longitude: value.longitude,
                })
            }
        },
        [tripReport_Stops, setTripReport_Stops, setCurrentLocation],
    )

    const handleArrTimeChange = (date: any) => {
        const formattedTime = dayjs(date).format('HH:mm')
        setArrTime(formattedTime)
        setTripReport_Stops({
            ...tripReport_Stops,
            arriveTime: formattedTime,
            arrTime: formattedTime,
        })
    }

    const handleDepTimeChange = (date: any) => {
        setDepTime(dayjs(date).format('HH:mm'))
        setTripReport_Stops({
            ...tripReport_Stops,
            depTime: dayjs(date).format('HH:mm'),
            departTime: dayjs(date).format('HH:mm'),
        })
    }

    // Initialize local state from tripReport_Stops
    useEffect(() => {
        if (tripReport_Stops) {
            setLocalPaxOn(tripReport_Stops.paxOn?.toString() || '')
            setLocalPaxOff(tripReport_Stops.paxOff?.toString() || '')
            setLocalVehicleOn(tripReport_Stops.vehicleOn?.toString() || '')
            setLocalVehicleOff(tripReport_Stops.vehicleOff?.toString() || '')
        }
    }, [
        tripReport_Stops?.paxOn,
        tripReport_Stops?.paxOff,
        tripReport_Stops?.vehicleOn,
        tripReport_Stops?.vehicleOff,
    ])

    useEffect(() => {
        if (selectedEvent) {
            setCurrentEvent(selectedEvent)
            getCurrentTripReport_Stop(selectedEvent?.id)
        }
    }, [selectedEvent])
    const offlineCreateDangerousGoodsChecklist = async () => {
        // createDangerousGoodsChecklist
        const delay = (ms: number) =>
            new Promise((resolve) => setTimeout(resolve, ms))
        await delay(2000)
        const data = await dangerousGoodsChecklistModel.save({
            id: generateUniqueId(),
        })
        setDgrChecklist(data)
    }
    useEffect(() => {
        if (currentEvent) {
            getCurrentTripReport_Stop(currentEvent?.id)
            setDgrChecklist(currentEvent?.dangerousGoodsChecklist)
        } else {
            // Initialize default values for new records
            if (!tripReport_Stops) {
                setTripReport_Stops({
                    paxOn: 0,
                    paxOff: 0,
                    vehicleOn: 0,
                    vehicleOff: 0,
                })
            }
        }
    }, [currentEvent])

    const getCurrentTripReport_Stop = async (id: any) => {
        if (offline) {
            // tripReport_Stop
            const event = await tripReport_StopModel.getById(id)
            if (event) {
                setDisplayDangerousGoods(
                    displayDangerousGoods
                        ? displayDangerousGoods
                        : event?.dangerousGoodsRecords?.nodes.length > 0,
                )
                setTripEvent(event)
                if (!tripReport_Stops) {
                    setBufferDgr(event?.dangerousGoodsRecords?.nodes)
                    setTripReport_Stops({
                        geoLocationID: event.stopLocationID,
                        arrTime: event?.arriveTime,
                        depTime: event.departTime,
                        paxOn: +event.paxJoined,
                        paxOff: +event.paxDeparted,
                        vehicleOn: +event.vehiclesJoined,
                        vehicleOff: +event.vehiclesDeparted,
                        otherCargo: event.otherCargo,
                        comments: event.comments,
                        lat: event.stopLocation?.lat,
                        long: event.stopLocation?.long,
                    })
                    setArrTime(event.arriveTime)
                    setDepTime(event.departTime)
                    if (event.stopLocation?.lat && event.stopLocation?.long) {
                        setCurrentLocation({
                            latitude: event.stopLocation?.lat,
                            longitude: event.stopLocation?.long,
                        })
                    }
                    if (event?.lat && event?.long) {
                        setCurrentLocation({
                            latitude: event?.lat,
                            longitude: event?.long,
                        })
                    }
                }
            }
        } else {
            tripReport_Stop({
                variables: {
                    id: id,
                },
            })
        }
    }

    const getBufferDgr = async (id: any) => {
        if (bufferDgr.length > 0) {
            const dgr = bufferDgr.map((d: any) => {
                return +d.id
            })
            if (offline) {
                // getDgrList
                const data = await dangerousGoodsRecordModel.getByIds([
                    ...dgr,
                    +id,
                ])
                setBufferDgr(data)
            } else {
                getDgrList({
                    variables: {
                        ids: [...dgr, +id],
                    },
                })
            }
        } else {
            if (offline) {
                // getDgrList
                const data = await dangerousGoodsRecordModel.getByIds([+id])
                setBufferDgr(data)
            } else {
                getDgrList({
                    variables: {
                        ids: [+id],
                    },
                })
            }
        }
    }

    const [getDgrList] = useLazyQuery(GetDangerousGoodsRecords, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (data) => {
            setBufferDgr(data.readDangerousGoodsRecords.nodes)
        },
        onError: (error) => {
            console.error('Error getting buffer dgr', error)
        },
    })

    const [tripReport_Stop] = useLazyQuery(GetTripReport_Stop, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const event = response.readOneTripReport_Stop
            if (event) {
                setDisplayDangerousGoods(
                    displayDangerousGoods
                        ? displayDangerousGoods
                        : event?.dangerousGoodsRecords?.nodes.length > 0,
                )
                setDisplayDangerousGoodsSailing(
                    displayDangerousGoodsSailing !== null
                        ? displayDangerousGoodsSailing
                        : event?.designatedDangerousGoodsSailing,
                )
                setTripEvent(event)
                if (!tripReport_Stops) {
                    setBufferDgr(event?.dangerousGoodsRecords?.nodes)
                    setTripReport_Stops({
                        geoLocationID: event.stopLocationID,
                        arrTime: event?.arriveTime,
                        depTime: event.departTime,
                        paxOn: +event.paxJoined,
                        paxOff: +event.paxDeparted,
                        vehicleOn: +event.vehiclesJoined,
                        vehicleOff: +event.vehiclesDeparted,
                        otherCargo: event.otherCargo,
                        comments: event.comments,
                        lat: event.stopLocation?.lat,
                        long: event.stopLocation?.long,
                        designatedDangerousGoodsSailing:
                            event.designatedDangerousGoodsSailing,
                    })
                    setArrTime(event.arriveTime)
                    setDepTime(event.departTime)
                    if (event.stopLocation?.lat && event.stopLocation?.long) {
                        setCurrentLocation({
                            latitude: event.stopLocation?.lat,
                            longitude: event.stopLocation?.long,
                        })
                    }
                    if (event?.lat && event?.long) {
                        setCurrentLocation({
                            latitude: event?.lat,
                            longitude: event?.long,
                        })
                    }
                }
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })

    const validateForm = () => {
        // Validate stopLocationID
        const stopLocationID = +tripReport_Stops?.geoLocationID
        if (!stopLocationID || stopLocationID <= 0) {
            toast({
                title: 'Error',
                description: 'Please select a trip stop location',
                variant: 'destructive',
            })
            return false
        }

        // Validate arriveTime - check both arrTime and tripReport_Stops.arriveTime/arrTime
        const arriveTimeValue =
            arrTime || tripReport_Stops?.arriveTime || tripReport_Stops?.arrTime

        // Use isEmpty but also check for false value since arrTime's initial state is false
        if (isEmpty(arriveTimeValue) || arriveTimeValue === false) {
            toast({
                title: 'Error',
                description: 'Please enter an arrival time',
                variant: 'destructive',
            })
            return false
        }

        return true
    }

    const handleSave = async () => {
        // Validate form before saving
        if (!validateForm()) {
            return
        }

        // Use local state values for the most current input data
        const paxOnValue =
            localPaxOn !== '' ? +localPaxOn : tripReport_Stops?.paxOn || 0
        const paxOffValue =
            localPaxOff !== '' ? +localPaxOff : tripReport_Stops?.paxOff || 0
        const vehicleOnValue =
            localVehicleOn !== ''
                ? +localVehicleOn
                : tripReport_Stops?.vehicleOn || 0
        const vehicleOffValue =
            localVehicleOff !== ''
                ? +localVehicleOff
                : tripReport_Stops?.vehicleOff || 0

        const variables = {
            input: {
                arriveTime:
                    arrTime ||
                    tripReport_Stops?.arriveTime ||
                    tripReport_Stops?.arrTime,
                departTime: depTime ? depTime : tripReport_Stops?.departTime,
                paxJoined: paxOnValue,
                paxDeparted: paxOffValue,
                vehiclesJoined: isNaN(vehicleOnValue) ? 0 : vehicleOnValue,
                vehiclesDeparted: isNaN(vehicleOffValue) ? 0 : vehicleOffValue,
                stopLocationID: +tripReport_Stops?.geoLocationID,
                otherCargo: tripReport_Stops?.otherCargo,
                comments: tripReport_Stops?.comments,
                lat: currentLocation.latitude.toString(),
                long: currentLocation.longitude.toString(),
                dangerousGoodsChecklistID: +dgrChecklist?.id,
                designatedDangerousGoodsSailing: displayDangerousGoodsSailing,
            },
        }
        if (currentEvent) {
            if (offline) {
                // updateTripReport_Stop
                const data = await tripReport_StopModel.save({
                    id: +selectedEvent?.id,
                    ...variables.input,
                })
                await getCurrentTripReport_Stop(data?.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
            } else {
                updateTripReport_Stop({
                    variables: {
                        input: {
                            id: +selectedEvent?.id,
                            ...variables.input,
                        },
                    },
                })
            }
        } else {
            // Set default values for new records
            variables.input.paxJoined = variables.input.paxJoined || 0
            variables.input.paxDeparted = variables.input.paxDeparted || 0

            if (offline) {
                // createTripReport_Stop
                const data = await tripReport_StopModel.save({
                    ...variables.input,
                    logBookEntrySectionID: currentTrip.id,
                    id: generateUniqueId(),
                })
                await getCurrentTripReport_Stop(data?.id)
                if (bufferDgr.length > 0) {
                    Promise.all(
                        bufferDgr.map(async (dgr: any) => {
                            // updateDangerousGoodsRecord
                            const dgrData =
                                await dangerousGoodsRecordModel.save({
                                    id: dgr.id,
                                    tripReport_StopID: data.id,
                                    type: dgr.type,
                                    comment: dgr.comment,
                                })
                            // Clear any existing toasts
                            if (currentEvent?.id > 0) {
                                await getCurrentTripReport_Stop(
                                    currentEvent?.id,
                                )
                            } else {
                                if (dgrData) {
                                    await getBufferDgr(dgrData.id)
                                }
                            }

                            // createDangerousGoodsChecklist
                            const dgChecklistData =
                                await dangerousGoodsChecklistModel.save({
                                    id: generateUniqueId(),
                                    tripReport_StopID: data.id,
                                    vesselSecuredToWharf:
                                        dgrChecklist?.vesselSecuredToWharf,
                                    bravoFlagRaised:
                                        dgrChecklist?.bravoFlagRaised,
                                    twoCrewLoadingVessel:
                                        dgrChecklist?.twoCrewLoadingVessel,
                                    fireHosesRiggedAndReady:
                                        dgrChecklist?.fireHosesRiggedAndReady,
                                    noSmokingSignagePosted:
                                        dgrChecklist?.noSmokingSignagePosted,
                                    spillKitAvailable:
                                        dgrChecklist?.spillKitAvailable,
                                    fireExtinguishersAvailable:
                                        dgrChecklist?.fireExtinguishersAvailable,
                                    dgDeclarationReceived:
                                        dgrChecklist?.dgDeclarationReceived,
                                    loadPlanReceived:
                                        dgrChecklist?.loadPlanReceived,
                                    msdsAvailable: dgrChecklist?.msdsAvailable,
                                    anyVehiclesSecureToVehicleDeck:
                                        dgrChecklist?.anyVehiclesSecureToVehicleDeck,
                                    safetyAnnouncement:
                                        dgrChecklist?.safetyAnnouncement,
                                    vehicleStationaryAndSecure:
                                        dgrChecklist?.vehicleStationaryAndSecure,
                                })
                            setDgrChecklist(dgChecklistData)
                        }),
                    )
                }
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
                closeModal()
            } else {
                createTripReport_Stop({
                    variables: {
                        input: {
                            ...variables.input,
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
        }
    }

    const [createDangerousGoodsChecklist] = useMutation(
        CreateDangerousGoodsChecklist,
        {
            onCompleted: (response) => {
                const data = response.createDangerousGoodsChecklist
                setDgrChecklist(data)
            },
            onError: (error) => {
                console.error('Error creating dangerous goods', error)
            },
        },
    )

    const [updateDangerousGoodsRecord] = useMutation(
        UpdateDangerousGoodsRecord,
        {
            onCompleted: (response) => {
                const data = response.updateDangerousGoodsRecord
                // Process the response
                currentEvent?.id > 0
                    ? getCurrentTripReport_Stop(currentEvent?.id)
                    : getBufferDgr(data.id)
            },
            onError: (error) => {
                console.error('Error updating dangerous goods record', error)
                toast({
                    title: 'Error',
                    description: 'Error updating dangerous goods record',
                    variant: 'destructive',
                })
            },
        },
    )

    const [createTripReport_Stop] = useMutation(CreateTripReport_Stop, {
        onCompleted: (response) => {
            const data = response.createTripReport_Stop
            getCurrentTripReport_Stop(data?.id)
            if (bufferDgr.length > 0) {
                bufferDgr.map((dgr: any) => {
                    updateDangerousGoodsRecord({
                        variables: {
                            input: {
                                id: dgr.id,
                                tripReport_StopID: data.id,
                                type: dgr.type,
                                comment: dgr.comment,
                            },
                        },
                    })
                    createDangerousGoodsChecklist({
                        variables: {
                            input: {
                                tripReport_StopID: data.id,
                                vesselSecuredToWharf:
                                    dgrChecklist?.vesselSecuredToWharf,
                                bravoFlagRaised: dgrChecklist?.bravoFlagRaised,
                                twoCrewLoadingVessel:
                                    dgrChecklist?.twoCrewLoadingVessel,
                                fireHosesRiggedAndReady:
                                    dgrChecklist?.fireHosesRiggedAndReady,
                                noSmokingSignagePosted:
                                    dgrChecklist?.noSmokingSignagePosted,
                                spillKitAvailable:
                                    dgrChecklist?.spillKitAvailable,
                                fireExtinguishersAvailable:
                                    dgrChecklist?.fireExtinguishersAvailable,
                                dgDeclarationReceived:
                                    dgrChecklist?.dgDeclarationReceived,
                                loadPlanReceived:
                                    dgrChecklist?.loadPlanReceived,
                                msdsAvailable: dgrChecklist?.msdsAvailable,
                                anyVehiclesSecureToVehicleDeck:
                                    dgrChecklist?.anyVehiclesSecureToVehicleDeck,
                                safetyAnnouncement:
                                    dgrChecklist?.safetyAnnouncement,
                                vehicleStationaryAndSecure:
                                    dgrChecklist?.vehicleStationaryAndSecure,
                            },
                        },
                    })
                })
            }
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), currentTrip.id],
            })
            closeModal()
        },
        onError: (error) => {
            console.error('Error creating passenger drop facility', error)
        },
    })

    const [updateTripReport_Stop] = useMutation(UpdateTripReport_Stop, {
        onCompleted: (response) => {
            const data = response.updateTripReport_Stop
            getCurrentTripReport_Stop(data?.id)
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), currentTrip.id],
            })
            closeModal()
        },
        onError: (error) => {
            console.error('Error updating passenger drop facility', error)
        },
    })

    return (
        <div className="space-y-8">
            <P className="max-w-[40rem] leading-loose">
                For recording trip stops where passengers, cargo and/or vehicles
                maybe getting on and off.
            </P>
            {displayField(type + 'Location') && (
                <Label
                    label="Location of trip stop"
                    htmlFor="trip-location"
                    className={`${locked ? 'pointer-events-none' : ''} my-4`}>
                    <LocationField
                        offline={offline}
                        setCurrentLocation={setCurrentLocation}
                        handleLocationChange={handleLocationChangeCallback}
                        currentEvent={memoizedCurrentEvent}
                    />
                </Label>
            )}
            {displayField(type + 'Arrival') && (
                <Label
                    label="Arrival Time"
                    htmlFor="arrival-time"
                    className={`${locked ? 'pointer-events-none' : ''} my-4`}>
                    <TimeField
                        time={
                            arrTime ||
                            tripReport_Stops?.arriveTime ||
                            tripReport_Stops?.arrTime ||
                            ''
                        }
                        handleTimeChange={handleArrTimeChange}
                        timeID="arrival-time"
                        fieldName="Arrival Time"
                    />
                </Label>
            )}
            {displayField(type + 'Departure') && (
                <Label
                    label="Departure Time"
                    htmlFor="departure-time"
                    className={`${locked ? 'pointer-events-none' : ''} my-4`}>
                    <TimeField
                        time={
                            depTime ? depTime : tripReport_Stops?.depTime ?? ''
                        }
                        handleTimeChange={handleDepTimeChange}
                        timeID="departure-time"
                        fieldName="Departure Time"
                    />
                </Label>
            )}
            {displayField(type + 'PaxPickDrop') && (
                <div
                    className={`${locked ? 'pointer-events-none' : ''} my-4 flex flex-row gap-4 w-full`}>
                    <Label
                        label="Passengers off"
                        htmlFor="paxOff"
                        className="w-full">
                        <Input
                            id="paxOff"
                            name="paxOff"
                            type="number"
                            value={localPaxOff}
                            placeholder="Pax off"
                            min="0"
                            onChange={handlePaxOffChange}
                            onBlur={handlePaxOffBlur}
                        />
                    </Label>
                    <Label
                        label="Passengers on"
                        htmlFor="paxOn"
                        className="w-full">
                        <Input
                            id="paxOn"
                            name="paxOn"
                            type="number"
                            value={localPaxOn}
                            placeholder="Pax on"
                            min="0"
                            onChange={handlePaxOnChange}
                            onBlur={handlePaxOnBlur}
                        />
                    </Label>
                </div>
            )}
            {displayField(type + 'VehiclePickDrop') && (
                <div
                    className={`${locked ? 'pointer-events-none' : ''} my-4 flex flex-row gap-4 w-full`}>
                    <Label
                        label="Vehicles on"
                        htmlFor="vehicleOn"
                        className="w-full">
                        <Input
                            id="vehicleOn"
                            name="vehicleOn"
                            type="number"
                            value={localVehicleOn}
                            placeholder="Vehicles getting on"
                            min="0"
                            onChange={handleVehicleOnChange}
                            onBlur={handleVehicleOnBlur}
                        />
                    </Label>
                    <Label
                        label="Vehicles off"
                        htmlFor="vehicleOff"
                        className="w-full">
                        <Input
                            id="vehicleOff"
                            name="vehicleOff"
                            type="number"
                            value={localVehicleOff}
                            placeholder="Vehicles getting off"
                            min="0"
                            onChange={handleVehicleOffChange}
                            onBlur={handleVehicleOffBlur}
                        />
                    </Label>
                </div>
            )}
            {displayField(type + 'OtherCargo') && (
                <Label
                    label="Cargo (if any)"
                    htmlFor="cargo-onOff"
                    className={`${locked ? 'pointer-events-none' : ''} my-4`}>
                    <Textarea
                        id="cargo-onOff"
                        placeholder="Other cargo on and off"
                        value={
                            cargoOnOff !== ''
                                ? cargoOnOff
                                : tripReport_Stops?.otherCargo || ''
                        }
                        onChange={(e) => {
                            setCargoOnOff(e.target.value)
                        }}
                        onBlur={(e) => {
                            setTripReport_Stops({
                                ...tripReport_Stops,
                                otherCargo: e.target.value,
                            })
                        }}
                    />
                </Label>
            )}
            {/* {vessel?.vesselSpecifics?.carriesDangerousGoods && ( */}
            <PVPDDGR
                offline={offline}
                locked={locked}
                currentTrip={currentTrip}
                logBookConfig={logBookConfig}
                selectedDGR={selectedDGR}
                setSelectedDGR={setSelectedDGR}
                members={members}
                displayDangerousGoods={displayDangerousGoods}
                setDisplayDangerousGoods={setDisplayDangerousGoods}
                displayDangerousGoodsSailing={displayDangerousGoodsSailing}
                setDisplayDangerousGoodsSailing={
                    setDisplayDangerousGoodsSailing
                }
                allDangerousGoods={allPVPDDangerousGoods}
                setAllDangerousGoods={setAllPVPDDangerousGoods}
                currentEvent={tripEvent}
            />
            {/* )} */}
            <Label
                label="Comments"
                htmlFor="comments"
                className={`${locked ? 'pointer-events-none' : ''} my-4`}>
                <Textarea
                    id="comments"
                    placeholder="Comments"
                    value={
                        comments !== ''
                            ? comments
                            : tripReport_Stops?.comments || ''
                    }
                    onChange={(e) => {
                        setComments(e.target.value)
                    }}
                    onBlur={(e) => {
                        setTripReport_Stops({
                            ...tripReport_Stops,
                            comments: e.target.value,
                        })
                    }}
                />
            </Label>
            <div className="flex justify-end gap-2">
                <Button
                    variant="back"
                    iconLeft={ArrowLeft}
                    onClick={() => closeModal()}>
                    Cancel
                </Button>
                <Button
                    variant="primary"
                    iconLeft={Check}
                    onClick={locked ? () => {} : handleSave}
                    disabled={locked}>
                    {selectedEvent ? 'Update' : 'Save'}
                </Button>
            </div>
        </div>
    )
}
