"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/actual-arrival-time.tsx":
/*!****************************************************!*\
  !*** ./src/app/ui/logbook/actual-arrival-time.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ActualArrival; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _components_ui_time_picker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/time-picker */ \"(app-pages-browser)/./src/components/ui/time-picker.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/offline/models/tripReport_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/tripReport_LogBookEntrySection.js\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ActualArrival(param) {\n    let { currentTrip, updateTripReport, tripReport, offline = false } = param;\n    var _tripReport_find, _tripReport_find1;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const tripReportModel = new _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_6__[\"default\"]();\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleTimeChange = async (date)=>{\n        const arrive = dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).format(\"YYYY-MM-DD HH:mm\") // Mukul: This is the format that the backend expects\n        ;\n        setTime(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).format(\"HH:mm\"));\n        if (offline) {\n            const data = await tripReportModel.save({\n                id: currentTrip.id,\n                arrive: arrive\n            });\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    data.id\n                ],\n                currentTripID: currentTrip.id,\n                key: \"arrive\",\n                value: arrive\n            });\n        } else {\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: currentTrip.id,\n                        arrive: arrive\n                    }\n                }\n            });\n        }\n    };\n    const [updateTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_8__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateTripReport_LogBookEntrySection, {\n        onCompleted: (data)=>{\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    data.id\n                ],\n                currentTripID: currentTrip.id,\n                key: \"arrive\",\n                value: time\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Failed to update arrival time\"\n            });\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (tripReport) {\n            var _tripReport_find, _tripReport_find1;\n            if (time !== (tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find === void 0 ? void 0 : _tripReport_find.arrive) ? convertTimeFormat(tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find1 = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find1 === void 0 ? void 0 : _tripReport_find1.arrive) : \"\") {\n                var _tripReport_find2, _tripReport_find3;\n                setTime((tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find2 = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find2 === void 0 ? void 0 : _tripReport_find2.arrive) ? convertTimeFormat(tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find3 = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find3 === void 0 ? void 0 : _tripReport_find3.arrive) : \"\");\n            }\n        }\n    }, [\n        tripReport\n    ]);\n    const convertTimeFormat = (time)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_2___default()(time).format(\"HH:mm\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n        className: \"w-full\",\n        label: \"Actual arrival time\",\n        htmlFor: \"actual-arrival-time\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_4__.TimePicker, {\n            value: time ? dayjs__WEBPACK_IMPORTED_MODULE_2___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"), \" \").concat(time)).toDate() : (tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find === void 0 ? void 0 : _tripReport_find.arrive) ? dayjs__WEBPACK_IMPORTED_MODULE_2___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"), \" \").concat(convertTimeFormat(tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find1 = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find1 === void 0 ? void 0 : _tripReport_find1.arrive))).toDate() : new Date(),\n            onChange: handleTimeChange,\n            use24Hour: true,\n            className: \"max-w-sm\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\actual-arrival-time.tsx\",\n            lineNumber: 112,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\actual-arrival-time.tsx\",\n        lineNumber: 108,\n        columnNumber: 9\n    }, this);\n}\n_s(ActualArrival, \"gZwbjmwxniMX7GVLdsxx91puErU=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_8__.useMutation\n    ];\n});\n_c = ActualArrival;\nvar _c;\n$RefreshReg$(_c, \"ActualArrival\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/actual-arrival-time.tsx\n"));

/***/ })

});