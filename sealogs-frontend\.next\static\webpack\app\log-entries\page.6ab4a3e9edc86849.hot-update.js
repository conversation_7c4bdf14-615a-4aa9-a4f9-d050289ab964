"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/events.tsx":
/*!***************************************!*\
  !*** ./src/app/ui/logbook/events.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Events; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _forms_vessel_rescue__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./forms/vessel-rescue */ \"(app-pages-browser)/./src/app/ui/logbook/forms/vessel-rescue.tsx\");\n/* harmony import */ var _forms_person_rescue__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./forms/person-rescue */ \"(app-pages-browser)/./src/app/ui/logbook/forms/person-rescue.tsx\");\n/* harmony import */ var _forms_restricted_visibility__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./forms/restricted-visibility */ \"(app-pages-browser)/./src/app/ui/logbook/forms/restricted-visibility.tsx\");\n/* harmony import */ var _forms_bar_crossing__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./forms/bar-crossing */ \"(app-pages-browser)/./src/app/ui/logbook/forms/bar-crossing.tsx\");\n/* harmony import */ var _forms_passenger_drop_facility__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./forms/passenger-drop-facility */ \"(app-pages-browser)/./src/app/ui/logbook/forms/passenger-drop-facility.tsx\");\n/* harmony import */ var _forms_tasking__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./forms/tasking */ \"(app-pages-browser)/./src/app/ui/logbook/forms/tasking.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _forms_crew_training_event__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./forms/crew-training-event */ \"(app-pages-browser)/./src/app/ui/logbook/forms/crew-training-event.tsx\");\n/* harmony import */ var _forms_supernumerary_event__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./forms/supernumerary-event */ \"(app-pages-browser)/./src/app/ui/logbook/forms/supernumerary-event.tsx\");\n/* harmony import */ var _forms_passenger_vehicle_pick_drop__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./forms/passenger-vehicle-pick-drop */ \"(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! lodash/uniqueId */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/uniqueId.js\");\n/* harmony import */ var lodash_uniqueId__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(lodash_uniqueId__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var _forms_refuelling_bunkering__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./forms/refuelling-bunkering */ \"(app-pages-browser)/./src/app/ui/logbook/forms/refuelling-bunkering.tsx\");\n/* harmony import */ var _app_lib_vesselTypes__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/app/lib/vesselTypes */ \"(app-pages-browser)/./src/app/lib/vesselTypes.tsx\");\n/* harmony import */ var _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/app/lib/logbook-configuration */ \"(app-pages-browser)/./src/app/lib/logbook-configuration/index.ts\");\n/* harmony import */ var _app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/app/helpers/userHelper */ \"(app-pages-browser)/./src/app/helpers/userHelper.ts\");\n/* harmony import */ var _forms_InfringementNotices__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./forms/InfringementNotices */ \"(app-pages-browser)/./src/app/ui/logbook/forms/InfringementNotices.tsx\");\n/* harmony import */ var _forms_trip_update__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./forms/trip-update */ \"(app-pages-browser)/./src/app/ui/logbook/forms/trip-update.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_21___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_21__);\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/accordion */ \"(app-pages-browser)/./src/components/ui/accordion.tsx\");\n/* harmony import */ var _components_ui_typography__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/ui/typography */ \"(app-pages-browser)/./src/components/ui/typography.tsx\");\n/* harmony import */ var _radio_logs_schedule__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./radio-logs-schedule */ \"(app-pages-browser)/./src/app/ui/logbook/radio-logs-schedule.tsx\");\n/* harmony import */ var _incident_record_incident_record_form__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! ../incident-record/incident-record-form */ \"(app-pages-browser)/./src/app/ui/incident-record/incident-record-form.tsx\");\n/* harmony import */ var _forms_pilot_transfer__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./forms/pilot-transfer */ \"(app-pages-browser)/./src/app/ui/logbook/forms/pilot-transfer.tsx\");\n/* harmony import */ var _app_lib_utils__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/app/lib/utils */ \"(app-pages-browser)/./src/app/lib/utils.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction Events(param) {\n    let { currentTrip, logBookConfig, updateTripReport, locked, geoLocations, tripReport, crewMembers, masterID, vessel, vessels, offline = false, setSelectedRow, setCurrentEventType, setCurrentStop, currentEventType, currentStop, tripReport_Stops, setTripReport_Stops, displayDangerousGoodsPvpd = false, displayDangerousGoodsPvpdSailing, setDisplayDangerousGoodsPvpd, setDisplayDangerousGoodsPvpdSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGRPVPD, setSelectedDGRPVPD, fuelLogs, logBookStartDate } = param;\n    var _currentTrip_tripReport_Stops, _currentTrip_tripEvents_nodes, _currentTrip_tripEvents, _currentTrip_tripReport_Stops_nodes, _currentTrip_tripReport_Stops1, _currentTrip_tripEvents_nodes1, _currentTrip_tripEvents1, _currentTrip_tripReport_Stops_nodes1, _currentTrip_tripReport_Stops2, _currentTrip_tripReport_Stops3;\n    _s();\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [openEventModal, setOpenEventModal] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [taskingEvents, setTaskingEvents] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(0);\n    const [accordionValue, setAccordionValue] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const vesselID = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams)().get(\"vesselID\") || \"0\";\n    const [permissions, setPermissions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [edit_tripActivity, setEdit_tripActivity] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [displayRadioLogs, setDisplayRadioLogs] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [activityTypeOptions, setActivityTypeOptions] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const init_permissions = ()=>{\n        if (permissions) {\n            if ((0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.hasPermission)(process.env.EDIT_LOGBOOKENTRY_ACTIVITY || \"EDIT_LOGBOOKENTRY_ACTIVITY\", permissions)) {\n                setEdit_tripActivity(true);\n            } else {\n                setEdit_tripActivity(false);\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        setPermissions(_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.getPermissions);\n        init_permissions();\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        init_permissions();\n        initData();\n    }, [\n        permissions\n    ]);\n    const initData = ()=>{\n        var _logBookConfig_customisedLogBookComponents;\n        const combinedFields = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents.nodes.filter((section)=>section.componentClass === \"SeaLogs\\\\EventType_LogBookComponent\" || section.componentClass === \"EventType_LogBookComponent\").reduce((acc, section)=>{\n            acc = acc.concat(section.customisedComponentFields.nodes);\n            return acc;\n        }, []);\n        const hasRescueType = combinedFields === null || combinedFields === void 0 ? void 0 : combinedFields.find((field)=>field.fieldName === \"VesselRescue\" || field.fieldName === \"HumanRescue\");\n        if (logBookConfig) {\n            const eventList = hasRescueType ? combinedFields.filter((field)=>!hasParent(field) && field.status !== \"Off\") : combinedFields === null || combinedFields === void 0 ? void 0 : combinedFields.filter((field)=>!hasParent(field) && field.status !== \"Off\" && field.fieldName !== \"TaskingStartUnderway\" && field.fieldName !== \"TaskingOnScene\" && field.fieldName !== \"TaskingOnTow\" && field.fieldName !== \"TaskingPaused\" && field.fieldName !== \"TaskingResumed\" && field.fieldName !== \"TaskingComplete\" && field.fieldName !== \"DangerousGoodsSailing\");\n            const filteredEvents = eventList === null || eventList === void 0 ? void 0 : eventList.map((event)=>({\n                    label: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_2__.getFieldName)(event).replace(/([a-z])([A-Z])/g, \"$1 $2\").replace(\"Passenger Arrival\", \"Arrival\").replace(\"Passenger Departure\", \"Departure\"),\n                    value: event.fieldName\n                })).filter((event, index, self)=>index === self.findIndex((e)=>e.value === event.value)).filter((event)=>// event?.value !== 'VesselRescue' &&\n                // event?.value !== 'HumanRescue' &&\n                // event?.value !== 'Supernumerary' &&\n                !isTowingField(event.value)).filter((event)=>checkVesselType(event.value));\n            // Add Incident Record as a custom activity type\n            // Incident Record is available for all vessel types\n            filteredEvents.push({\n                label: \"Incident Record\",\n                value: \"IncidentRecord\"\n            });\n            // Add Infringement Notices as a custom activity type if vessel type allows it\n            // InfringementNotices is only available for vessel types 0 and 1\n            const vesselTypeID = _app_lib_vesselTypes__WEBPACK_IMPORTED_MODULE_16__[\"default\"].findIndex((type)=>type == (vessel === null || vessel === void 0 ? void 0 : vessel.vesselType));\n            if ([\n                0,\n                1\n            ].includes(vesselTypeID)) {\n                filteredEvents.push({\n                    label: \"Infringement Notices\",\n                    value: \"InfringementNotice\"\n                });\n            }\n            if (permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.hasPermission)(\"RECORD_TRAINING\", permissions)) {\n                setEvents(sortFilteredEvents(filteredEvents));\n            } else {\n                var _filteredEvents_filter;\n                setEvents(sortFilteredEvents((_filteredEvents_filter = filteredEvents === null || filteredEvents === void 0 ? void 0 : filteredEvents.filter((event)=>event.value !== \"CrewTraining\")) !== null && _filteredEvents_filter !== void 0 ? _filteredEvents_filter : []));\n            }\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        initData();\n    }, [\n        logBookConfig\n    ]);\n    const checkVesselType = (field)=>{\n        const vesselTypeID = _app_lib_vesselTypes__WEBPACK_IMPORTED_MODULE_16__[\"default\"].findIndex((type)=>type == (vessel === null || vessel === void 0 ? void 0 : vessel.vesselType));\n        const config = _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_17__.SLALL_LogBookFields.find((localField)=>localField.componentClass === \"EventType_LogBookComponent\");\n        const isVesselType = config === null || config === void 0 ? void 0 : config.items.find((localField)=>field === localField.value && localField.vesselType.includes(vesselTypeID));\n        return isVesselType ? true : false;\n    };\n    const sortFilteredEvents = (events)=>{\n        var _currentTrip_tripEvents_nodes, _currentTrip_tripEvents;\n        // Always ensure tasking events are available in the base events array\n        const taskingEvents = [\n            \"TaskingStartUnderway\",\n            \"TaskingOnScene\",\n            \"TaskingOnTow\",\n            \"TaskingComplete\",\n            \"TaskingPaused\",\n            \"TaskingResumed\"\n        ];\n        // Add missing tasking events to the events array\n        const eventsWithTasking = [\n            ...events\n        ];\n        taskingEvents.forEach((taskingType)=>{\n            if (!eventsWithTasking.find((event)=>event.value === taskingType)) {\n                eventsWithTasking.push({\n                    label: taskingType.replace(/([a-z])([A-Z])/g, \"$1 $2\"),\n                    value: taskingType\n                });\n            }\n        });\n        if (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes = _currentTrip_tripEvents.nodes) === null || _currentTrip_tripEvents_nodes === void 0 ? void 0 : _currentTrip_tripEvents_nodes.find((event)=>{\n            var _event_eventType_Tasking, _event_eventType_Tasking1;\n            return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\" && ((_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.status) === \"Open\";\n        })) {\n            var _currentTrip_tripEvents_nodes_filter, _currentTrip_tripEvents_nodes_filter1;\n            const openTask = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter = currentTrip.tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\";\n            })) === null || _currentTrip_tripEvents_nodes_filter === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter.length;\n            const pausedTask = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter1 = currentTrip.tripEvents.nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingPaused\";\n            })) === null || _currentTrip_tripEvents_nodes_filter1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter1.length;\n            const sortedEvents = [\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingStartUnderway\" && openTask - pausedTask < 1).map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingOnScene\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingOnTow\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingComplete\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingPaused\").map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>event.value === \"TaskingResumed\" && pausedTask > 0).map((event)=>({\n                        ...event,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    })),\n                ...eventsWithTasking.filter((event)=>!event.value.includes(\"Tasking\"))\n            ];\n            return sortedEvents;\n        }\n        return eventsWithTasking;\n    };\n    /*const colourStyles: StylesConfig = {\r\n        option: (\r\n            styles: any,\r\n            {\r\n                data,\r\n                isDisabled,\r\n                isFocused,\r\n                isSelected,\r\n            }: { data: any; isDisabled: any; isFocused: any; isSelected: any },\r\n        ) => {\r\n            const color = data.color\r\n            return {\r\n                ...styles,\r\n                backgroundColor: isDisabled\r\n                    ? undefined\r\n                    : isSelected\r\n                      ? data.bgColor\r\n                      : isFocused\r\n                        ? data.bgColor\r\n                        : data.bgColor + '60',\r\n                color: data.color,\r\n            }\r\n        },\r\n        singleValue: (styles: any, data: any) => ({\r\n            ...styles,\r\n            color: events.find((option: any) => option.value == data.data.value)\r\n                ?.color,\r\n        }),\r\n    }*/ const formatTime = (time)=>time.slice(0, 5);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        var _currentTrip_tripEvents_nodes_filter, _currentTrip_tripEvents;\n        const taskingEvents = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter = _currentTrip_tripEvents.nodes.filter((event)=>{\n            var _event_eventType_Tasking, _event_eventType_Tasking1, _event_eventType_Tasking2;\n            return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && ((event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\" || (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.type) === \"TaskingPaused\") && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.status) === \"Open\";\n        })) === null || _currentTrip_tripEvents_nodes_filter === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter.length;\n        setTaskingEvents(taskingEvents);\n    }, [\n        currentTrip\n    ]);\n    const hasParent = (field)=>{\n        const config = _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_17__.SLALL_LogBookFields.find((localField)=>localField.componentClass === \"EventType_LogBookComponent\");\n        const hasGroup = config === null || config === void 0 ? void 0 : config.items.find((localField)=>field.fieldName === localField.value && localField.groupTo);\n        return hasGroup ? true : false;\n    };\n    const isTowingField = (field)=>{\n        const config = _app_lib_logbook_configuration__WEBPACK_IMPORTED_MODULE_17__.SLALL_LogBookFields.find((localField)=>localField.componentClass === \"EventType_LogBookComponent\");\n        const isTowingCategory = config === null || config === void 0 ? void 0 : config.items.find((localField)=>field === localField.value && localField.type === \"TowingSubCategory\");\n        return isTowingCategory ? true : false;\n    };\n    const handleEventChange = (event)=>{\n        setCurrentEvent(false);\n        setCurrentStop(false);\n        setTripReport_Stops(false);\n        setDisplayDangerousGoodsPvpd(false);\n        setDisplayDangerousGoodsPvpdSailing(null);\n        fetchActivityTypes();\n        setCurrentEventType(event);\n    };\n    // const handleSetOpenEventModal = () => {\n    // setOpenEventModal(!openEventModal)\n    // }\n    const handleSetCurrentEventType = ()=>{\n        setCurrentEventType(false);\n        // Reset accordion state to properly close it\n        setAccordionValue(\"\");\n        setSelectedRow(0);\n        setCurrentEvent(false);\n        setCurrentStop(false);\n    // setOpenEventModal(false)\n    };\n    const previousDropEvent = (currentEvent)=>{\n        var _currentTrip_tripEvents;\n        const previousEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.find((event)=>(event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id));\n        return previousEvent;\n    };\n    const mainTaskingEvent = (currentEvent)=>{\n        var _currentTrip_tripEvents;\n        const mainEvent = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : _currentTrip_tripEvents.nodes.filter((event)=>{\n            var _event_eventType_Tasking;\n            return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : event.id) !== (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\";\n        });\n        return mainEvent;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        if (events) {\n            var _currentTrip_tripEvents_nodes_filter, _currentTrip_tripEvents_nodes, _currentTrip_tripEvents;\n            let options = [];\n            if (taskingEvents === 0) {\n                options = events.filter((event)=>(event === null || event === void 0 ? void 0 : event.value) !== \"TaskingOnScene\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingOnTow\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingPaused\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingResumed\" && (event === null || event === void 0 ? void 0 : event.value) !== \"TaskingComplete\");\n                // Ensure TaskingStartUnderway is always available when no tasking events are open\n                const taskingStartUnderwayOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingStartUnderway\");\n                if (taskingStartUnderwayOption && !options.find((option)=>option.value === \"TaskingStartUnderway\")) {\n                    options.push(taskingStartUnderwayOption);\n                } else if (!taskingStartUnderwayOption) {\n                    // If TaskingStartUnderway is not in the events array, create it manually\n                    options.push({\n                        label: \"Tasking Start Underway\",\n                        value: \"TaskingStartUnderway\"\n                    });\n                }\n            } else {\n                var _currentTrip_tripEvents1, _currentTrip_tripEvents2, _currentTrip_tripEvents3;\n                const taskingOpen = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : _currentTrip_tripEvents1.nodes.filter((event)=>{\n                    var _event_eventType_Tasking, _event_eventType_Tasking1;\n                    return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingStartUnderway\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.status) === \"Open\";\n                });\n                const taskingPaused = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents2 = currentTrip.tripEvents) === null || _currentTrip_tripEvents2 === void 0 ? void 0 : _currentTrip_tripEvents2.nodes.filter((event)=>{\n                    var _event_eventType_Tasking;\n                    return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingPaused\";\n                });\n                const taskingResumed = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents3 = currentTrip.tripEvents) === null || _currentTrip_tripEvents3 === void 0 ? void 0 : _currentTrip_tripEvents3.nodes.filter((event)=>{\n                    var _event_eventType_Tasking;\n                    return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingResumed\";\n                });\n                if ((taskingOpen === null || taskingOpen === void 0 ? void 0 : taskingOpen.length) > 0) {\n                    if ((taskingPaused === null || taskingPaused === void 0 ? void 0 : taskingPaused.length) === (taskingResumed === null || taskingResumed === void 0 ? void 0 : taskingResumed.length)) {\n                        options = events.filter((event)=>(event === null || event === void 0 ? void 0 : event.value) !== \"TaskingResumed\");\n                    }\n                    if ((taskingPaused === null || taskingPaused === void 0 ? void 0 : taskingPaused.length) > (taskingResumed === null || taskingResumed === void 0 ? void 0 : taskingResumed.length)) {\n                        options = events.filter((event)=>event.value !== \"TaskingOnScene\" && event.value !== \"TaskingOnTow\" && event.value !== \"TaskingPaused\");\n                    }\n                } else {\n                    options = events.filter((event)=>event.value !== \"TaskingOnScene\" && event.value !== \"TaskingOnTow\" && event.value !== \"TaskingPaused\" && event.value !== \"TaskingResumed\" && event.value !== \"TaskingComplete\");\n                    // Ensure TaskingStartUnderway is available when no open tasking events exist\n                    const taskingStartUnderwayOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingStartUnderway\");\n                    if (taskingStartUnderwayOption && !options.find((option)=>option.value === \"TaskingStartUnderway\")) {\n                        options.push(taskingStartUnderwayOption);\n                    } else if (!taskingStartUnderwayOption) {\n                        // If TaskingStartUnderway is not in the events array, create it manually\n                        options.push({\n                            label: \"Tasking Start Underway\",\n                            value: \"TaskingStartUnderway\"\n                        });\n                    }\n                }\n            }\n            // When taskingPaused > 0, ensure TaskingResumed and TaskingStartUnderway are available\n            const taskingPausedCount = (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes = _currentTrip_tripEvents.nodes) === null || _currentTrip_tripEvents_nodes === void 0 ? void 0 : (_currentTrip_tripEvents_nodes_filter = _currentTrip_tripEvents_nodes.filter((event)=>{\n                var _event_eventType_Tasking;\n                return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) === \"TaskingPaused\";\n            })) === null || _currentTrip_tripEvents_nodes_filter === void 0 ? void 0 : _currentTrip_tripEvents_nodes_filter.length) || 0;\n            if (taskingPausedCount > 0) {\n                // Find TaskingResumed and TaskingStartUnderway from the original events array\n                const taskingResumedOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingResumed\");\n                const taskingStartUnderwayOption = events.find((event)=>(event === null || event === void 0 ? void 0 : event.value) === \"TaskingStartUnderway\");\n                // Add TaskingResumed if it exists in events but not in current options\n                if (taskingResumedOption && !options.find((option)=>option.value === \"TaskingResumed\")) {\n                    options.push(taskingResumedOption);\n                }\n                // Add TaskingStartUnderway if it exists in events but not in current options\n                if (taskingStartUnderwayOption && !options.find((option)=>option.value === \"TaskingStartUnderway\")) {\n                    options.push(taskingStartUnderwayOption);\n                } else if (!taskingStartUnderwayOption) {\n                    // If TaskingStartUnderway is not in the events array, create it manually\n                    options.push({\n                        label: \"Tasking Start Underway\",\n                        value: \"TaskingStartUnderway\"\n                    });\n                }\n            }\n            options = options.map((option)=>{\n                if (option.value.includes(\"Tasking\") && option.value !== \"TaskingStartUnderway\" && !option.className) {\n                    return {\n                        ...option,\n                        className: \"bg-fire-bush-100 text-fire-bush-600 border border-fire-bush-500 hover:bg-fire-bush-50 hover:border-fire-bush-200\"\n                    };\n                }\n                return option;\n            });\n            // Remove duplicate by checking the options.value\n            options = options.filter((option, index, self)=>index === self.findIndex((o)=>o.value === option.value));\n            // Remove InfringementNotices from options because there's already InfringementNotice (without an 's').\n            options = options.filter((option)=>option.value !== \"InfringementNotices\");\n            // Remove HumanRescue and VesselRescue from options since it's already included in Tasking\n            options = options.filter((option)=>option.value !== \"HumanRescue\" && option.value !== \"VesselRescue\");\n            setActivityTypeOptions(options);\n        }\n    }, [\n        events,\n        currentTrip,\n        taskingEvents\n    ]);\n    const fetchActivityTypes = ()=>{\n        initData();\n    };\n    // Memoized function to handle stop accordion item clicks\n    const handleStopAccordionItemClick = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((eventId)=>{\n        return ()=>{\n            // Toggle accordion state\n            if (accordionValue === \"stop_\".concat(eventId)) {\n                setAccordionValue(\"\");\n                setSelectedRow(0);\n                setCurrentEventType([]);\n                setCurrentEvent(false);\n                setCurrentStop(false);\n            } else {\n                var _currentTrip_tripReport_Stops;\n                setAccordionValue(\"stop_\".concat(eventId));\n                setSelectedRow(eventId);\n                setCurrentEventType({\n                    label: \"Passenger/vehicle pickup/drop off\",\n                    value: \"PassengerVehiclePickDrop\"\n                });\n                // Find the event by ID\n                const event = currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops === void 0 ? void 0 : _currentTrip_tripReport_Stops.nodes.find((stop)=>stop.id === eventId);\n                setCurrentStop(event);\n                setDisplayDangerousGoodsPvpd(false);\n                setDisplayDangerousGoodsPvpdSailing(null);\n                setTripReport_Stops(false);\n            }\n        };\n    }, [\n        accordionValue,\n        currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops === void 0 ? void 0 : _currentTrip_tripReport_Stops.nodes,\n        setSelectedRow,\n        setCurrentEventType,\n        setCurrentEvent,\n        setCurrentStop,\n        setDisplayDangerousGoodsPvpd,\n        setDisplayDangerousGoodsPvpdSailing,\n        setTripReport_Stops\n    ]);\n    // Memoized function to generate stop display text\n    const getStopDisplayText = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)((event)=>{\n        var _event_stopLocation, _event_stopLocation1;\n        return \"Passenger / Vehicle Pick & Drop - \".concat((event === null || event === void 0 ? void 0 : event.arriveTime) ? (event === null || event === void 0 ? void 0 : event.arriveTime) + \" (arr)\" : \"\", \" \").concat((event === null || event === void 0 ? void 0 : event.arriveTime) && (event === null || event === void 0 ? void 0 : event.departTime) ? \"-\" : \"\", \" \").concat((event === null || event === void 0 ? void 0 : event.departTime) ? (event === null || event === void 0 ? void 0 : event.departTime) + \" (dep)\" : \"\", \" \").concat((event === null || event === void 0 ? void 0 : (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.title) ? event === null || event === void 0 ? void 0 : (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.title : \"\");\n    }, []);\n    const shouldIndent = (event)=>{\n        var _event_eventType_Tasking;\n        return event.eventCategory === \"Tasking\" && ((_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type) !== \"TaskingStartUnderway\";\n    };\n    const getEventLabel = (event)=>{\n        var _event_eventType_PassengerDropFacility_type_replace_replace, _event_eventType_PassengerDropFacility_type_replace, _event_eventType_PassengerDropFacility_type, _event_eventType_PassengerDropFacility, _event_eventType_Tasking;\n        return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type = _event_eventType_PassengerDropFacility.type) === null || _event_eventType_PassengerDropFacility_type === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace = _event_eventType_PassengerDropFacility_type.replace(/([a-z])([A-Z])/g, \"$1 $2\")) === null || _event_eventType_PassengerDropFacility_type_replace === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace_replace = _event_eventType_PassengerDropFacility_type_replace.replace(\"Passenger Arrival\", \"Arrival\")) === null || _event_eventType_PassengerDropFacility_type_replace_replace === void 0 ? void 0 : _event_eventType_PassengerDropFacility_type_replace_replace.replace(\"Passenger Departure\", \"Departure\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type.replace(/([a-z])([A-Z])/g, \"$1 $2\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"EventSupernumerary\" ? \"Supernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory.replace(/([a-z])([A-Z])/g, \"$1 $2\");\n    };\n    const getEventValue = (event)=>{\n        var _event_eventType_PassengerDropFacility, _event_eventType_Tasking;\n        return (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility === void 0 ? void 0 : _event_eventType_PassengerDropFacility.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Supernumerary\" ? \"EventSupernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory;\n    };\n    const getFuelTotals = (fuelLogs)=>{\n        const totalFuel = fuelLogs.reduce((acc, log)=>{\n            return acc + (log === null || log === void 0 ? void 0 : log.fuelAdded);\n        }, 0);\n        const totalCost = fuelLogs.reduce((acc, log)=>{\n            return acc + (log === null || log === void 0 ? void 0 : log.fuelAdded) * (log === null || log === void 0 ? void 0 : log.costPerLitre);\n        }, 0);\n        return \" - Total Fuel Added: \" + totalFuel + \"L, Total Cost: $\" + totalCost;\n    };\n    const getEventDisplayText = (event)=>{\n        var _eventType_geoLocation;\n        const category = event.eventCategory;\n        const eventType = event[\"eventType_\".concat(category)];\n        const geoLocation = eventType === null || eventType === void 0 ? void 0 : (_eventType_geoLocation = eventType.geoLocation) === null || _eventType_geoLocation === void 0 ? void 0 : _eventType_geoLocation.title;\n        const title = eventType === null || eventType === void 0 ? void 0 : eventType.title;\n        switch(category){\n            case \"PassengerDropFacility\":\n                var _eventType_type_replace_replace, _eventType_type_replace, _eventType_type;\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.time) ? eventType.time + \" - \" : \"\") + (eventType === null || eventType === void 0 ? void 0 : (_eventType_type = eventType.type) === null || _eventType_type === void 0 ? void 0 : (_eventType_type_replace = _eventType_type.replace(/([a-z])([A-Z])/g, \"$1 $2\")) === null || _eventType_type_replace === void 0 ? void 0 : (_eventType_type_replace_replace = _eventType_type_replace.replace(\"Passenger Arrival\", \"Arrival\")) === null || _eventType_type_replace_replace === void 0 ? void 0 : _eventType_type_replace_replace.replace(\"Passenger Departure\", \"Departure\")) + (title ? \" - \" + title : \"\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"Tasking\":\n                var _eventType_type1;\n                return (eventType === null || eventType === void 0 ? void 0 : eventType.time) + \" - \" + (eventType === null || eventType === void 0 ? void 0 : (_eventType_type1 = eventType.type) === null || _eventType_type1 === void 0 ? void 0 : _eventType_type1.replace(/([a-z])([A-Z])/g, \"$1 $2\")) + (title ? \" - \" + title : \"\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"BarCrossing\":\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.time) ? eventType.time + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"RefuellingBunkering\":\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.date) ? dayjs__WEBPACK_IMPORTED_MODULE_21___default()(eventType.date).format(\"HH:mm\") + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (geoLocation ? \" - \" + geoLocation : \"\");\n            case \"RestrictedVisibility\":\n                var _eventType_startLocation;\n                return ((eventType === null || eventType === void 0 ? void 0 : eventType.crossingTime) ? eventType.crossingTime + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + ((eventType === null || eventType === void 0 ? void 0 : (_eventType_startLocation = eventType.startLocation) === null || _eventType_startLocation === void 0 ? void 0 : _eventType_startLocation.title) ? \" - \" + eventType.startLocation.title : \"\");\n            case \"TripUpdate\":\n                var _event_tripUpdate, _event_tripUpdate_geoLocation, _event_tripUpdate1, _event_tripUpdate_geoLocation1, _event_tripUpdate2;\n                return (((_event_tripUpdate = event.tripUpdate) === null || _event_tripUpdate === void 0 ? void 0 : _event_tripUpdate.date) ? dayjs__WEBPACK_IMPORTED_MODULE_21___default()(event.tripUpdate.date).format(\"HH:mm\") + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_tripUpdate1 = event.tripUpdate) === null || _event_tripUpdate1 === void 0 ? void 0 : (_event_tripUpdate_geoLocation = _event_tripUpdate1.geoLocation) === null || _event_tripUpdate_geoLocation === void 0 ? void 0 : _event_tripUpdate_geoLocation.title) ? \" - \" + ((_event_tripUpdate2 = event.tripUpdate) === null || _event_tripUpdate2 === void 0 ? void 0 : (_event_tripUpdate_geoLocation1 = _event_tripUpdate2.geoLocation) === null || _event_tripUpdate_geoLocation1 === void 0 ? void 0 : _event_tripUpdate_geoLocation1.title) : \"\");\n            case \"EventSupernumerary\":\n                var _event_supernumerary, _event_supernumerary1, _event_supernumerary2;\n                return ((event === null || event === void 0 ? void 0 : (_event_supernumerary = event.supernumerary) === null || _event_supernumerary === void 0 ? void 0 : _event_supernumerary.briefingTime) ? (event === null || event === void 0 ? void 0 : (_event_supernumerary1 = event.supernumerary) === null || _event_supernumerary1 === void 0 ? void 0 : _event_supernumerary1.briefingTime) + \" - \" : \"\") + \"Supernumerary\" + ((event === null || event === void 0 ? void 0 : (_event_supernumerary2 = event.supernumerary) === null || _event_supernumerary2 === void 0 ? void 0 : _event_supernumerary2.title) ? \" - \" + (event === null || event === void 0 ? void 0 : event.supernumerary.title) : \"\");\n            case \"IncidentRecord\":\n                var _event_incidentRecord, _event_incidentRecord1;\n                return (((_event_incidentRecord = event.incidentRecord) === null || _event_incidentRecord === void 0 ? void 0 : _event_incidentRecord.startDate) ? dayjs__WEBPACK_IMPORTED_MODULE_21___default()(event.incidentRecord.startDate).format(\"HH:mm\") + \" - \" : \"\") + \"Incident Record\" + (((_event_incidentRecord1 = event.incidentRecord) === null || _event_incidentRecord1 === void 0 ? void 0 : _event_incidentRecord1.title) ? \" - \" + event.incidentRecord.title : \"\");\n            case \"InfringementNotice\":\n                var _event_infringementNotice, _event_infringementNotice1, _event_infringementNotice2;\n                return ((event === null || event === void 0 ? void 0 : (_event_infringementNotice = event.infringementNotice) === null || _event_infringementNotice === void 0 ? void 0 : _event_infringementNotice.time) ? dayjs__WEBPACK_IMPORTED_MODULE_21___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_21___default()().format(\"YYYY-MM-DD\"), \" \").concat(event === null || event === void 0 ? void 0 : event.infringementNotice.time)).format(\"HH:mm\") + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + ((event === null || event === void 0 ? void 0 : (_event_infringementNotice1 = event.infringementNotice) === null || _event_infringementNotice1 === void 0 ? void 0 : _event_infringementNotice1.geoLocation.title) ? \" - \" + (event === null || event === void 0 ? void 0 : (_event_infringementNotice2 = event.infringementNotice) === null || _event_infringementNotice2 === void 0 ? void 0 : _event_infringementNotice2.geoLocation.title) : \"\");\n            case \"CrewTraining\":\n                var _event_crewTraining, _event_crewTraining_geoLocation, _event_crewTraining1, _event_crewTraining_geoLocation1, _event_crewTraining2;\n                return (((_event_crewTraining = event.crewTraining) === null || _event_crewTraining === void 0 ? void 0 : _event_crewTraining.startTime) ? event.crewTraining.startTime + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_crewTraining1 = event.crewTraining) === null || _event_crewTraining1 === void 0 ? void 0 : (_event_crewTraining_geoLocation = _event_crewTraining1.geoLocation) === null || _event_crewTraining_geoLocation === void 0 ? void 0 : _event_crewTraining_geoLocation.title) ? \" - \" + ((_event_crewTraining2 = event.crewTraining) === null || _event_crewTraining2 === void 0 ? void 0 : (_event_crewTraining_geoLocation1 = _event_crewTraining2.geoLocation) === null || _event_crewTraining_geoLocation1 === void 0 ? void 0 : _event_crewTraining_geoLocation1.title) : \"\");\n            case \"VesselRescue\":\n                var _event_eventType_VesselRescue_mission, _event_eventType_VesselRescue, _event_eventType_VesselRescue_mission1, _event_eventType_VesselRescue1, _event_eventType_VesselRescue2;\n                return (((_event_eventType_VesselRescue = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue === void 0 ? void 0 : (_event_eventType_VesselRescue_mission = _event_eventType_VesselRescue.mission) === null || _event_eventType_VesselRescue_mission === void 0 ? void 0 : _event_eventType_VesselRescue_mission.completedAt) ? ((_event_eventType_VesselRescue1 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue1 === void 0 ? void 0 : (_event_eventType_VesselRescue_mission1 = _event_eventType_VesselRescue1.mission) === null || _event_eventType_VesselRescue_mission1 === void 0 ? void 0 : _event_eventType_VesselRescue_mission1.completedAt) + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_eventType_VesselRescue2 = event.eventType_VesselRescue) === null || _event_eventType_VesselRescue2 === void 0 ? void 0 : _event_eventType_VesselRescue2.vesselName) ? \" - \" + event.eventType_VesselRescue.vesselName : \"\");\n            case \"HumanRescue\":\n                var _event_eventType_PersonRescue_mission, _event_eventType_PersonRescue, _event_eventType_PersonRescue_mission1, _event_eventType_PersonRescue1, _event_eventType_PersonRescue2;\n                return (((_event_eventType_PersonRescue = event.eventType_PersonRescue) === null || _event_eventType_PersonRescue === void 0 ? void 0 : (_event_eventType_PersonRescue_mission = _event_eventType_PersonRescue.mission) === null || _event_eventType_PersonRescue_mission === void 0 ? void 0 : _event_eventType_PersonRescue_mission.completedAt) ? ((_event_eventType_PersonRescue1 = event.eventType_PersonRescue) === null || _event_eventType_PersonRescue1 === void 0 ? void 0 : (_event_eventType_PersonRescue_mission1 = _event_eventType_PersonRescue1.mission) === null || _event_eventType_PersonRescue_mission1 === void 0 ? void 0 : _event_eventType_PersonRescue_mission1.completedAt) + \" - \" : \"\") + category.replace(/([a-z])([A-Z])/g, \"$1 $2\") + (((_event_eventType_PersonRescue2 = event.eventType_PersonRescue) === null || _event_eventType_PersonRescue2 === void 0 ? void 0 : _event_eventType_PersonRescue2.personName) ? \" - \" + event.eventType_PersonRescue.personName : \"\");\n            default:\n                return category.replace(/([a-z])([A-Z])/g, \"$1 $2\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_25__.H4, {\n                            children: \"ACTIVITIES\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 864,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_typography__WEBPACK_IMPORTED_MODULE_25__.P, {\n                            children: \"Record the events that happen during a voyage in this section.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 865,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                    lineNumber: 863,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                lineNumber: 862,\n                columnNumber: 13\n            }, this),\n            (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents = currentTrip.tripEvents) === null || _currentTrip_tripEvents === void 0 ? void 0 : (_currentTrip_tripEvents_nodes = _currentTrip_tripEvents.nodes) === null || _currentTrip_tripEvents_nodes === void 0 ? void 0 : _currentTrip_tripEvents_nodes.length) > 0 || (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops1 = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops1 === void 0 ? void 0 : (_currentTrip_tripReport_Stops_nodes = _currentTrip_tripReport_Stops1.nodes) === null || _currentTrip_tripReport_Stops_nodes === void 0 ? void 0 : _currentTrip_tripReport_Stops_nodes.length) > 0 || !currentEvent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: ((currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripEvents1 = currentTrip.tripEvents) === null || _currentTrip_tripEvents1 === void 0 ? void 0 : (_currentTrip_tripEvents_nodes1 = _currentTrip_tripEvents1.nodes) === null || _currentTrip_tripEvents_nodes1 === void 0 ? void 0 : _currentTrip_tripEvents_nodes1.length) > 0 || (currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops2 = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops2 === void 0 ? void 0 : (_currentTrip_tripReport_Stops_nodes1 = _currentTrip_tripReport_Stops2.nodes) === null || _currentTrip_tripReport_Stops_nodes1 === void 0 ? void 0 : _currentTrip_tripReport_Stops_nodes1.length) > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.Accordion, {\n                    type: \"single\",\n                    collapsible: true,\n                    value: accordionValue,\n                    onValueChange: (value)=>{\n                        setAccordionValue(value);\n                        // If we're closing the accordion, reset the state\n                        if (value === \"\") {\n                            setSelectedRow(0);\n                            // setOpenEventModal(false)\n                            setCurrentEventType([]);\n                            setCurrentEvent(false);\n                            setCurrentStop(false);\n                        }\n                    },\n                    children: [\n                        currentTrip === null || currentTrip === void 0 ? void 0 : currentTrip.tripEvents.nodes.map((event, index)=>{\n                            var _event_eventType_PassengerDropFacility_type_replace_replace, _event_eventType_PassengerDropFacility_type_replace, _event_eventType_PassengerDropFacility_type, _event_eventType_PassengerDropFacility, _event_eventType_Tasking, _event_eventType_PassengerDropFacility1, _event_eventType_Tasking1, _event_eventType_Tasking2, _event_eventType_Tasking3, _event_eventType_RefuellingBunkering_fuelLog_nodes, _event_eventType_RefuellingBunkering_fuelLog, _event_eventType_RefuellingBunkering, _event_eventType_RefuellingBunkering_fuelLog1, _event_eventType_RefuellingBunkering1, _event_eventType_Tasking4, _event_eventType_Tasking5, _event_eventType_Tasking6, _event_eventType_Tasking7;\n                            // Generate event label and value outside the JSX\n                            const eventLabel = (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type = _event_eventType_PassengerDropFacility.type) === null || _event_eventType_PassengerDropFacility_type === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace = _event_eventType_PassengerDropFacility_type.replace(/([a-z])([A-Z])/g, \"$1 $2\")) === null || _event_eventType_PassengerDropFacility_type_replace === void 0 ? void 0 : (_event_eventType_PassengerDropFacility_type_replace_replace = _event_eventType_PassengerDropFacility_type_replace.replace(\"Passenger Arrival\", \"Arrival\")) === null || _event_eventType_PassengerDropFacility_type_replace_replace === void 0 ? void 0 : _event_eventType_PassengerDropFacility_type_replace_replace.replace(\"Passenger Departure\", \"Departure\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking = event.eventType_Tasking) === null || _event_eventType_Tasking === void 0 ? void 0 : _event_eventType_Tasking.type.replace(/([a-z])([A-Z])/g, \"$1 $2\") : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"EventSupernumerary\" ? \"Supernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory.replace(/([a-z])([A-Z])/g, \"$1 $2\");\n                            const eventValue = (event === null || event === void 0 ? void 0 : event.eventCategory) === \"PassengerDropFacility\" ? event === null || event === void 0 ? void 0 : (_event_eventType_PassengerDropFacility1 = event.eventType_PassengerDropFacility) === null || _event_eventType_PassengerDropFacility1 === void 0 ? void 0 : _event_eventType_PassengerDropFacility1.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" ? event === null || event === void 0 ? void 0 : (_event_eventType_Tasking1 = event.eventType_Tasking) === null || _event_eventType_Tasking1 === void 0 ? void 0 : _event_eventType_Tasking1.type : (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Supernumerary\" ? \"EventSupernumerary\" : event === null || event === void 0 ? void 0 : event.eventCategory;\n                            // Generate event display text\n                            const eventDisplayText = getEventDisplayText(event);\n                            // Handle click on accordion item\n                            const handleAccordionItemClick = ()=>{\n                                // Toggle accordion state\n                                if (accordionValue === event.id.toString()) {\n                                    setAccordionValue(\"\");\n                                    setSelectedRow(0);\n                                    // setOpenEventModal(false)\n                                    setCurrentEventType([]);\n                                    setCurrentEvent(false);\n                                    setCurrentStop(false);\n                                } else {\n                                    setAccordionValue(event.id.toString());\n                                    setSelectedRow(event.id);\n                                    // setOpenEventModal(true)\n                                    setCurrentEventType({\n                                        label: eventLabel,\n                                        value: eventValue\n                                    });\n                                    setCurrentEvent(event);\n                                    setTripReport_Stops(false);\n                                    setDisplayDangerousGoodsPvpd(false);\n                                    setDisplayDangerousGoodsPvpdSailing(null);\n                                }\n                            };\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionItem, {\n                                value: event.id.toString(),\n                                className: (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking2 = event.eventType_Tasking) === null || _event_eventType_Tasking2 === void 0 ? void 0 : _event_eventType_Tasking2.type) !== \"TaskingStartUnderway\" ? \"ml-[1.5rem]\" : \"\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionTrigger, {\n                                        onClick: handleAccordionItemClick,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center relative justify-between w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col inset-y-0 items-center justify-center absolute -left-[41px] sm:-left-[46px] w-5\",\n                                                    children: ((event === null || event === void 0 ? void 0 : event.eventCategory) !== \"Tasking\" || (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking3 = event.eventType_Tasking) === null || _event_eventType_Tasking3 === void 0 ? void 0 : _event_eventType_Tasking3.type) == \"TaskingStartUnderway\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: (0,_app_lib_utils__WEBPACK_IMPORTED_MODULE_29__.cn)(\"size-[11px] z-10 rounded-full\", currentEvent.id === event.id ? \"border border-primary bg-curious-blue-200\" : currentEvent.eventCategory === event.eventCategory ? \"border border-primary bg-curious-blue-200\" : \"border border-cool-wedgewood-200 bg-outer-space-50\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                        lineNumber: 1000,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 993,\n                                                    columnNumber: 53\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-left\",\n                                                    children: [\n                                                        eventDisplayText,\n                                                        (event === null || event === void 0 ? void 0 : event.eventCategory) === \"RefuellingBunkering\" && (event === null || event === void 0 ? void 0 : (_event_eventType_RefuellingBunkering = event.eventType_RefuellingBunkering) === null || _event_eventType_RefuellingBunkering === void 0 ? void 0 : (_event_eventType_RefuellingBunkering_fuelLog = _event_eventType_RefuellingBunkering.fuelLog) === null || _event_eventType_RefuellingBunkering_fuelLog === void 0 ? void 0 : (_event_eventType_RefuellingBunkering_fuelLog_nodes = _event_eventType_RefuellingBunkering_fuelLog.nodes) === null || _event_eventType_RefuellingBunkering_fuelLog_nodes === void 0 ? void 0 : _event_eventType_RefuellingBunkering_fuelLog_nodes.length) > 0 && getFuelTotals(event === null || event === void 0 ? void 0 : (_event_eventType_RefuellingBunkering1 = event.eventType_RefuellingBunkering) === null || _event_eventType_RefuellingBunkering1 === void 0 ? void 0 : (_event_eventType_RefuellingBunkering_fuelLog1 = _event_eventType_RefuellingBunkering1.fuelLog) === null || _event_eventType_RefuellingBunkering_fuelLog1 === void 0 ? void 0 : _event_eventType_RefuellingBunkering_fuelLog1.nodes)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1014,\n                                                    columnNumber: 53\n                                                }, this),\n                                                (event === null || event === void 0 ? void 0 : event.eventCategory) === \"Tasking\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking4 = event.eventType_Tasking) === null || _event_eventType_Tasking4 === void 0 ? void 0 : _event_eventType_Tasking4.type) === \"TaskingStartUnderway\" && (event === null || event === void 0 ? void 0 : (_event_eventType_Tasking5 = event.eventType_Tasking) === null || _event_eventType_Tasking5 === void 0 ? void 0 : _event_eventType_Tasking5.status) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"\".concat((event === null || event === void 0 ? void 0 : (_event_eventType_Tasking6 = event.eventType_Tasking) === null || _event_eventType_Tasking6 === void 0 ? void 0 : _event_eventType_Tasking6.status) === \"Open\" ? \"text-bright-turquoise-600\" : \"\", \" pr-2\"),\n                                                    children: event === null || event === void 0 ? void 0 : (_event_eventType_Tasking7 = event.eventType_Tasking) === null || _event_eventType_Tasking7 === void 0 ? void 0 : _event_eventType_Tasking7.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1036,\n                                                    columnNumber: 61\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                            lineNumber: 992,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 988,\n                                        columnNumber: 45\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionContent, {\n                                        className: \"pb-4\",\n                                        children: currentEventType && currentEvent && currentEvent.id === event.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                currentEventType.value === \"VesselRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_vessel_rescue__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1055,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"HumanRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_person_rescue__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1085,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"RestrictedVisibility\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_restricted_visibility__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity,\n                                                    members: crewMembers\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1115,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"BarCrossing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_bar_crossing__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    members: crewMembers,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1148,\n                                                    columnNumber: 65\n                                                }, this),\n                                                (currentEventType.value === \"PassengerArrival\" || currentEventType.value === \"PassengerDeparture\" || currentEventType.value === \"WaterTaxiService\" || currentEventType.value === \"ScheduledPassengerService\") && //TODO: update this form\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_drop_facility__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    inLogbook: true,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    type: currentEventType.value,\n                                                    logBookConfig: logBookConfig,\n                                                    previousDropEvent: previousDropEvent(currentEvent),\n                                                    vessel: vessel,\n                                                    locked: locked || !edit_tripActivity,\n                                                    fuelLogs: fuelLogs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1188,\n                                                    columnNumber: 65\n                                                }, this),\n                                                (currentEventType.value === \"TaskingStartUnderway\" || currentEventType.value === \"TaskingOnScene\" || currentEventType.value === \"TaskingOnTow\" || currentEventType.value === \"TaskingPaused\" || currentEventType.value === \"TaskingResumed\" || currentEventType.value === \"TaskingComplete\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_tasking__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    offline: offline,\n                                                    geoLocations: geoLocations,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    type: currentEventType.value,\n                                                    logBookConfig: logBookConfig,\n                                                    previousDropEvent: mainTaskingEvent(currentEvent),\n                                                    vessel: vessel,\n                                                    members: crewMembers,\n                                                    locked: locked || !edit_tripActivity,\n                                                    fuelLogs: fuelLogs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1244,\n                                                    columnNumber: 65\n                                                }, this),\n                                                permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: currentEventType.value === \"CrewTraining\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_crew_training_event__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        offline: offline,\n                                                        vesselId: +vesselID,\n                                                        trainingTypeId: 0,\n                                                        currentTrip: currentTrip,\n                                                        updateTripReport: updateTripReport,\n                                                        selectedEvent: currentEvent,\n                                                        tripReport: tripReport,\n                                                        closeModal: handleSetCurrentEventType,\n                                                        crewMembers: crewMembers,\n                                                        masterID: masterID,\n                                                        logBookConfig: logBookConfig,\n                                                        vessels: vessels,\n                                                        locked: locked || !edit_tripActivity,\n                                                        logBookStartDate: logBookStartDate\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                        lineNumber: 1298,\n                                                        columnNumber: 77\n                                                    }, this)\n                                                }, void 0, false),\n                                                currentEventType.value === \"EventSupernumerary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_supernumerary_event__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    offline: offline,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    tripReport: tripReport,\n                                                    selectedEvent: currentEvent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1348,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"RefuellingBunkering\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_refuelling_bunkering__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    logBookConfig: logBookConfig,\n                                                    locked: locked || !edit_tripActivity,\n                                                    mainFuelLogs: fuelLogs\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1378,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"TripUpdate\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_trip_update__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1414,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"PilotTransfer\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_pilot_transfer__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    locked: locked || !edit_tripActivity\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1441,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"InfringementNotice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_InfringementNotices__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    offline: offline,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    selectedEvent: currentEvent,\n                                                    tripReport: tripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    crewMembers: crewMembers,\n                                                    locked: locked || !edit_tripActivity,\n                                                    visibility: // selectedRow ===\n                                                    //     event.id &&\n                                                    currentEventType && currentEvent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1468,\n                                                    columnNumber: 65\n                                                }, this),\n                                                currentEventType.value === \"IncidentRecord\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_incident_record_incident_record_form__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    id: (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.incidentRecordID) || 0,\n                                                    currentTrip: currentTrip,\n                                                    updateTripReport: updateTripReport,\n                                                    closeModal: handleSetCurrentEventType,\n                                                    inLogbook: true,\n                                                    selectedEvent: currentEvent,\n                                                    offline: offline,\n                                                    tripReport: tripReport\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                    lineNumber: 1504,\n                                                    columnNumber: 65\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 1047,\n                                        columnNumber: 45\n                                    }, this)\n                                ]\n                            }, index + \"_events\", true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                lineNumber: 976,\n                                columnNumber: 41\n                            }, this);\n                        }),\n                        currentTrip === null || currentTrip === void 0 ? void 0 : (_currentTrip_tripReport_Stops3 = currentTrip.tripReport_Stops) === null || _currentTrip_tripReport_Stops3 === void 0 ? void 0 : _currentTrip_tripReport_Stops3.nodes.map((event, index)=>{\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionItem, {\n                                value: \"stop_\".concat(event.id),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionTrigger, {\n                                        onClick: handleStopAccordionItemClick(event.id),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-left\",\n                                                children: getStopDisplayText(event)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                lineNumber: 1552,\n                                                columnNumber: 53\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                            lineNumber: 1551,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 1547,\n                                        columnNumber: 45\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_accordion__WEBPACK_IMPORTED_MODULE_24__.AccordionContent, {\n                                        className: \"pb-4\",\n                                        children: currentEventType && currentStop && currentStop.id === event.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: currentEventType.value === \"PassengerVehiclePickDrop\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_vehicle_pick_drop__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                offline: offline,\n                                                geoLocations: geoLocations,\n                                                updateTripReport: updateTripReport,\n                                                currentTrip: currentTrip,\n                                                selectedEvent: currentStop,\n                                                tripReport: tripReport,\n                                                closeModal: handleSetCurrentEventType,\n                                                type: currentEventType.value,\n                                                logBookConfig: logBookConfig,\n                                                members: crewMembers,\n                                                locked: locked || !edit_tripActivity,\n                                                tripReport_Stops: tripReport_Stops,\n                                                setTripReport_Stops: setTripReport_Stops,\n                                                displayDangerousGoods: displayDangerousGoodsPvpd,\n                                                setDisplayDangerousGoods: setDisplayDangerousGoodsPvpd,\n                                                displayDangerousGoodsSailing: displayDangerousGoodsPvpdSailing,\n                                                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsPvpdSailing,\n                                                allPVPDDangerousGoods: allPVPDDangerousGoods,\n                                                setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                                                selectedDGR: selectedDGRPVPD,\n                                                setSelectedDGR: setSelectedDGRPVPD\n                                            }, \"pvpd-\".concat(event.id), false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                                lineNumber: 1567,\n                                                columnNumber: 65\n                                            }, this)\n                                        }, void 0, false)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                        lineNumber: 1559,\n                                        columnNumber: 45\n                                    }, this)\n                                ]\n                            }, index + \"_stops\", true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                lineNumber: 1544,\n                                columnNumber: 41\n                            }, this);\n                        })\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                    lineNumber: 878,\n                    columnNumber: 25\n                }, this)\n            }, void 0, false) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-start gap-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_23__.Label, {\n                    position: \"left\",\n                    className: \"w-full\",\n                    label: \"Activity Type \",\n                    children: activityTypeOptions.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_22__.Combobox, {\n                        id: \"task-assigned\",\n                        options: activityTypeOptions,\n                        value: currentEventType,\n                        onChange: handleEventChange,\n                        title: \"Activity Type\",\n                        placeholder: \"Activity Type\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                        lineNumber: 1653,\n                        columnNumber: 25\n                    }, this) : // Failsafe - in case the activity types are not loaded.\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"outline\",\n                            onClick: fetchActivityTypes,\n                            children: \"Refresh activity types\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1664,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                        lineNumber: 1663,\n                        columnNumber: 25\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                    lineNumber: 1648,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                lineNumber: 1646,\n                columnNumber: 13\n            }, this),\n            currentEventType && !currentEvent && !currentStop && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    currentEventType.value === \"VesselRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_vessel_rescue__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1698,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"HumanRescue\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_person_rescue__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1712,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"RestrictedVisibility\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_restricted_visibility__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity,\n                            members: crewMembers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1726,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"BarCrossing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_bar_crossing__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            members: crewMembers,\n                            closeModal: handleSetCurrentEventType,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1741,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    (currentEventType.value === \"PassengerArrival\" || currentEventType.value === \"PassengerDeparture\" || currentEventType.value === \"WaterTaxiService\" || currentEventType.value === \"ScheduledPassengerService\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_drop_facility__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            type: currentEventType.value,\n                            logBookConfig: logBookConfig,\n                            inLogbook: true,\n                            previousDropEvent: previousDropEvent(currentEvent),\n                            vessel: vessel,\n                            locked: locked || !edit_tripActivity,\n                            fuelLogs: fuelLogs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1760,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    (currentEventType.value === \"TaskingStartUnderway\" || currentEventType.value === \"TaskingOnScene\" || currentEventType.value === \"TaskingOnTow\" || currentEventType.value === \"TaskingPaused\" || currentEventType.value === \"TaskingResumed\" || currentEventType.value === \"TaskingComplete\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_tasking__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            inLogbook: true,\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            type: currentEventType.value,\n                            logBookConfig: logBookConfig,\n                            previousDropEvent: mainTaskingEvent(currentEvent),\n                            vessel: vessel,\n                            members: crewMembers,\n                            locked: locked || !edit_tripActivity,\n                            fuelLogs: fuelLogs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1787,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    permissions && (0,_app_helpers_userHelper__WEBPACK_IMPORTED_MODULE_18__.hasPermission)(\"RECORD_TRAINING\", permissions) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: currentEventType.value === \"CrewTraining\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_crew_training_event__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                offline: offline,\n                                vesselId: +vesselID,\n                                trainingTypeId: 0,\n                                currentTrip: currentTrip,\n                                updateTripReport: updateTripReport,\n                                selectedEvent: currentEvent,\n                                tripReport: tripReport,\n                                closeModal: handleSetCurrentEventType,\n                                crewMembers: crewMembers,\n                                masterID: masterID,\n                                logBookConfig: logBookConfig,\n                                vessels: vessels,\n                                locked: locked || !edit_tripActivity,\n                                logBookStartDate: logBookStartDate\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                                lineNumber: 1813,\n                                columnNumber: 41\n                            }, this)\n                        }, void 0, false)\n                    }, void 0, false),\n                    currentEventType.value === \"EventSupernumerary\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_supernumerary_event__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            inLogbook: true,\n                            offline: offline,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity,\n                            closeModal: handleSetCurrentEventType,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            tripReport: tripReport,\n                            selectedEvent: currentEvent\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1839,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"PassengerVehiclePickDrop\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_passenger_vehicle_pick_drop__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            offline: offline,\n                            geoLocations: geoLocations,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentStop,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            type: currentEventType.value,\n                            logBookConfig: logBookConfig,\n                            members: crewMembers,\n                            locked: locked || !edit_tripActivity,\n                            tripReport_Stops: tripReport_Stops,\n                            setTripReport_Stops: setTripReport_Stops,\n                            displayDangerousGoods: displayDangerousGoodsPvpd,\n                            setDisplayDangerousGoods: setDisplayDangerousGoodsPvpd,\n                            displayDangerousGoodsSailing: displayDangerousGoodsPvpdSailing,\n                            setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsPvpdSailing,\n                            allPVPDDangerousGoods: allPVPDDangerousGoods,\n                            setAllPVPDDangerousGoods: setAllPVPDDangerousGoods,\n                            selectedDGR: selectedDGRPVPD,\n                            setSelectedDGR: setSelectedDGRPVPD\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1854,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"RefuellingBunkering\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_refuelling_bunkering__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            logBookConfig: logBookConfig,\n                            locked: locked || !edit_tripActivity,\n                            mainFuelLogs: fuelLogs\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1891,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"TripUpdate\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_trip_update__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            locked: locked || !edit_tripActivity\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1906,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"InfringementNotice\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_forms_InfringementNotices__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            offline: offline,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            selectedEvent: currentEvent,\n                            tripReport: tripReport,\n                            closeModal: handleSetCurrentEventType,\n                            crewMembers: crewMembers,\n                            locked: locked || !edit_tripActivity,\n                            visibility: currentEventType && !currentEvent && !currentStop\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1933,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false),\n                    currentEventType.value === \"IncidentRecord\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_incident_record_incident_record_form__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                            id: (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.incidentRecordID) || 0,\n                            currentTrip: currentTrip,\n                            updateTripReport: updateTripReport,\n                            closeModal: handleSetCurrentEventType,\n                            inLogbook: true,\n                            selectedEvent: currentEvent,\n                            offline: offline,\n                            tripReport: tripReport\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                            lineNumber: 1952,\n                            columnNumber: 29\n                        }, this)\n                    }, void 0, false)\n                ]\n            }, void 0, true),\n            currentTrip.tripReportScheduleID > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radio_logs_schedule__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                open: displayRadioLogs,\n                setOpen: setDisplayRadioLogs,\n                currentTrip: currentTrip\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n                lineNumber: 1967,\n                columnNumber: 17\n            }, this)\n        ]\n    }, lodash_uniqueId__WEBPACK_IMPORTED_MODULE_14___default()(), true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\events.tsx\",\n        lineNumber: 861,\n        columnNumber: 9\n    }, this);\n}\n_s(Events, \"3rrNMu4zV6K2k4YQIASNWHgDV2c=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useSearchParams\n    ];\n});\n_c = Events;\nvar _c;\n$RefreshReg$(_c, \"Events\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/events.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx":
/*!******************************************************************!*\
  !*** ./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx ***!
  \******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ PassengerVehiclePickDrop; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/lib/graphQL/query */ \"(app-pages-browser)/./src/app/lib/graphQL/query/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_time__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../components/time */ \"(app-pages-browser)/./src/app/ui/logbook/components/time.tsx\");\n/* harmony import */ var _components_location__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../components/location */ \"(app-pages-browser)/./src/app/ui/logbook/components/location.tsx\");\n/* harmony import */ var _pvpddgr__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../pvpddgr */ \"(app-pages-browser)/./src/app/ui/logbook/pvpddgr.tsx\");\n/* harmony import */ var _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsRecord */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsRecord.js\");\n/* harmony import */ var _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/offline/models/tripReport_Stop */ \"(app-pages-browser)/./src/app/offline/models/tripReport_Stop.js\");\n/* harmony import */ var _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/app/offline/models/geoLocation */ \"(app-pages-browser)/./src/app/offline/models/geoLocation.js\");\n/* harmony import */ var _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/offline/models/dangerousGoodsChecklist */ \"(app-pages-browser)/./src/app/offline/models/dangerousGoodsChecklist.js\");\n/* harmony import */ var _app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/app/offline/helpers/functions */ \"(app-pages-browser)/./src/app/offline/helpers/functions.ts\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/ui/comboBox */ \"(app-pages-browser)/./src/components/ui/comboBox.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.474.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Create model instances outside component to prevent re-instantiation on every render\nconst dangerousGoodsRecordModel = new _app_offline_models_dangerousGoodsRecord__WEBPACK_IMPORTED_MODULE_10__[\"default\"]();\nconst tripReport_StopModel = new _app_offline_models_tripReport_Stop__WEBPACK_IMPORTED_MODULE_11__[\"default\"]();\nconst geoLocationModel = new _app_offline_models_geoLocation__WEBPACK_IMPORTED_MODULE_12__[\"default\"]();\nconst dangerousGoodsChecklistModel = new _app_offline_models_dangerousGoodsChecklist__WEBPACK_IMPORTED_MODULE_13__[\"default\"]();\n\n\n\n\n\n\n\nfunction PassengerVehiclePickDrop(param) {\n    let { geoLocations, currentTrip = false, updateTripReport, selectedEvent = false, tripReport, closeModal, type, logBookConfig, members, locked, tripReport_Stops, setTripReport_Stops, displayDangerousGoods = false, displayDangerousGoodsSailing, setDisplayDangerousGoods, setDisplayDangerousGoodsSailing, allPVPDDangerousGoods, setAllPVPDDangerousGoods, selectedDGR, setSelectedDGR, offline = false } = param;\n    _s();\n    const [locations, setLocations] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [arrTime, setArrTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [depTime, setDepTime] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [cargoOnOff, setCargoOnOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [currentEvent, setCurrentEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(selectedEvent);\n    const [parentLocation, setParentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [bufferDgr, setBufferDgr] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [dgrChecklist, setDgrChecklist] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [tripEvent, setTripEvent] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [location, setLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [currentLocation, setCurrentLocation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        latitude: \"\",\n        longitude: \"\"\n    });\n    const [openNewLocationDialog, setOpenNewLocationDialog] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // Local state for input values to prevent focus loss\n    const [localPaxOn, setLocalPaxOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localPaxOff, setLocalPaxOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOn, setLocalVehicleOn] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [localVehicleOff, setLocalVehicleOff] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // Memoize displayField results to prevent re-computation on every render\n    const displayFieldResults = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        var _logBookConfig_customisedLogBookComponents_nodes, _logBookConfig_customisedLogBookComponents;\n        const eventTypesConfig = logBookConfig === null || logBookConfig === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents = logBookConfig.customisedLogBookComponents) === null || _logBookConfig_customisedLogBookComponents === void 0 ? void 0 : (_logBookConfig_customisedLogBookComponents_nodes = _logBookConfig_customisedLogBookComponents.nodes) === null || _logBookConfig_customisedLogBookComponents_nodes === void 0 ? void 0 : _logBookConfig_customisedLogBookComponents_nodes.filter((node)=>node.componentClass === \"EventType_LogBookComponent\");\n        const fieldMap = new Map();\n        if ((eventTypesConfig === null || eventTypesConfig === void 0 ? void 0 : eventTypesConfig.length) > 0) {\n            var _eventTypesConfig__customisedComponentFields, _eventTypesConfig_;\n            (_eventTypesConfig_ = eventTypesConfig[0]) === null || _eventTypesConfig_ === void 0 ? void 0 : (_eventTypesConfig__customisedComponentFields = _eventTypesConfig_.customisedComponentFields) === null || _eventTypesConfig__customisedComponentFields === void 0 ? void 0 : _eventTypesConfig__customisedComponentFields.nodes.forEach((field)=>{\n                if (field.status !== \"Off\") {\n                    fieldMap.set(field.fieldName, true);\n                }\n            });\n        }\n        return fieldMap;\n    }, [\n        logBookConfig\n    ]);\n    const displayField = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((fieldName)=>{\n        return displayFieldResults.get(fieldName) || false;\n    }, [\n        displayFieldResults\n    ]);\n    // Stable onChange handlers that only update local state\n    const handlePaxOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOff(e.target.value);\n    }, []);\n    const handlePaxOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalPaxOn(e.target.value);\n    }, []);\n    const handleVehicleOnChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOn(e.target.value);\n    }, []);\n    const handleVehicleOffChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setLocalVehicleOff(e.target.value);\n    }, []);\n    // Stable onBlur handlers that update the main state\n    const handlePaxOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handlePaxOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                paxOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOnBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOn: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    const handleVehicleOffBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setTripReport_Stops((prev)=>({\n                ...prev,\n                vehicleOff: e.target.value === \"\" ? 0 : +e.target.value\n            }));\n    }, [\n        setTripReport_Stops\n    ]);\n    // Memoize currentEvent object to prevent inline object creation\n    const memoizedCurrentEvent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            geoLocationID: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID,\n            lat: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.lat,\n            long: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.long\n        }), [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.lat,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.long\n    ]);\n    // Memoize other callback functions\n    const handleLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((value)=>{\n        // If value is null or undefined, clear the location\n        if (!value) {\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: 0,\n                lat: null,\n                long: null\n            });\n            return;\n        }\n        // Handle \"Add new location\" option\n        if (value.value === \"newLocation\") {\n            if (\"geolocation\" in navigator) {\n                navigator.geolocation.getCurrentPosition((param)=>{\n                    let { coords } = param;\n                    const { latitude, longitude } = coords;\n                    setLocation({\n                        latitude,\n                        longitude\n                    });\n                    setOpenNewLocationDialog(true);\n                });\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: \"Geolocation is not supported by your browser\",\n                    variant: \"destructive\"\n                });\n                setOpenNewLocationDialog(true);\n            }\n            return;\n        }\n        // Check if the value is from dropdown selection (has 'value' property)\n        if (value.value) {\n            // Handle location selected from dropdown\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: +value.value,\n                lat: null,\n                long: null\n            });\n            // If the value object has latitude and longitude, update currentLocation\n            if (value.latitude !== undefined && value.longitude !== undefined) {\n                setCurrentLocation({\n                    latitude: value.latitude,\n                    longitude: value.longitude\n                });\n            }\n        } else if (value.latitude !== undefined && value.longitude !== undefined) {\n            // Handle direct coordinates input\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: 0,\n                lat: value.latitude,\n                long: value.longitude\n            });\n            // Update currentLocation\n            setCurrentLocation({\n                latitude: value.latitude,\n                longitude: value.longitude\n            });\n        }\n    }, [\n        tripReport_Stops,\n        setTripReport_Stops,\n        toast,\n        setLocation,\n        setOpenNewLocationDialog,\n        setCurrentLocation\n    ]);\n    const handleParentLocationChangeCallback = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((selectedLocation)=>{\n        setParentLocation((selectedLocation === null || selectedLocation === void 0 ? void 0 : selectedLocation.value) || null);\n    }, [\n        setParentLocation\n    ]);\n    const handleArrTimeChange = (date)=>{\n        const formattedTime = dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\");\n        setArrTime(formattedTime);\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            arriveTime: formattedTime,\n            arrTime: formattedTime\n        });\n    };\n    const handleDepTimeChange = (date)=>{\n        setDepTime(dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"));\n        setTripReport_Stops({\n            ...tripReport_Stops,\n            depTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\"),\n            departTime: dayjs__WEBPACK_IMPORTED_MODULE_1___default()(date).format(\"HH:mm\")\n        });\n    };\n    // Initialize local state from tripReport_Stops\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (tripReport_Stops) {\n            var _tripReport_Stops_paxOn, _tripReport_Stops_paxOff, _tripReport_Stops_vehicleOn, _tripReport_Stops_vehicleOff;\n            setLocalPaxOn(((_tripReport_Stops_paxOn = tripReport_Stops.paxOn) === null || _tripReport_Stops_paxOn === void 0 ? void 0 : _tripReport_Stops_paxOn.toString()) || \"\");\n            setLocalPaxOff(((_tripReport_Stops_paxOff = tripReport_Stops.paxOff) === null || _tripReport_Stops_paxOff === void 0 ? void 0 : _tripReport_Stops_paxOff.toString()) || \"\");\n            setLocalVehicleOn(((_tripReport_Stops_vehicleOn = tripReport_Stops.vehicleOn) === null || _tripReport_Stops_vehicleOn === void 0 ? void 0 : _tripReport_Stops_vehicleOn.toString()) || \"\");\n            setLocalVehicleOff(((_tripReport_Stops_vehicleOff = tripReport_Stops.vehicleOff) === null || _tripReport_Stops_vehicleOff === void 0 ? void 0 : _tripReport_Stops_vehicleOff.toString()) || \"\");\n        }\n    }, [\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn,\n        tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (selectedEvent) {\n            setCurrentEvent(selectedEvent);\n            getCurrentTripReport_Stop(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id);\n        }\n    }, [\n        selectedEvent\n    ]);\n    const offlineCreateDangerousGoodsChecklist = async ()=>{\n        // createDangerousGoodsChecklist\n        const delay = (ms)=>new Promise((resolve)=>setTimeout(resolve, ms));\n        await delay(2000);\n        const data = await dangerousGoodsChecklistModel.save({\n            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n        });\n        setDgrChecklist(data);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (currentEvent) {\n            getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n            setDgrChecklist(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.dangerousGoodsChecklist);\n        } else {\n            // Initialize default values for new records\n            if (!tripReport_Stops) {\n                setTripReport_Stops({\n                    paxOn: 0,\n                    paxOff: 0,\n                    vehicleOn: 0,\n                    vehicleOff: 0\n                });\n            }\n        }\n    }, [\n        currentEvent\n    ]);\n    const getCurrentTripReport_Stop = async (id)=>{\n        if (offline) {\n            // tripReport_Stop\n            const event = await tripReport_StopModel.getById(id);\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long\n                    });\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) && ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long)) {\n                        var _event_stopLocation4, _event_stopLocation5;\n                        setCurrentLocation({\n                            latitude: (_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat,\n                            longitude: (_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        setCurrentLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        } else {\n            tripReport_Stop({\n                variables: {\n                    id: id\n                }\n            });\n        }\n    };\n    const getBufferDgr = async (id)=>{\n        if (bufferDgr.length > 0) {\n            const dgr = bufferDgr.map((d)=>{\n                return +d.id;\n            });\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    ...dgr,\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            ...dgr,\n                            +id\n                        ]\n                    }\n                });\n            }\n        } else {\n            if (offline) {\n                // getDgrList\n                const data = await dangerousGoodsRecordModel.getByIds([\n                    +id\n                ]);\n                setBufferDgr(data);\n            } else {\n                getDgrList({\n                    variables: {\n                        ids: [\n                            +id\n                        ]\n                    }\n                });\n            }\n        }\n    };\n    const [getDgrList] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetDangerousGoodsRecords, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (data)=>{\n            setBufferDgr(data.readDangerousGoodsRecords.nodes);\n        },\n        onError: (error)=>{\n            console.error(\"Error getting buffer dgr\", error);\n        }\n    });\n    const [tripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery)(_app_lib_graphQL_query__WEBPACK_IMPORTED_MODULE_5__.GetTripReport_Stop, {\n        fetchPolicy: \"cache-and-network\",\n        onCompleted: (response)=>{\n            const event = response.readOneTripReport_Stop;\n            if (event) {\n                var _event_dangerousGoodsRecords;\n                setDisplayDangerousGoods(displayDangerousGoods ? displayDangerousGoods : (event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords === void 0 ? void 0 : _event_dangerousGoodsRecords.nodes.length) > 0);\n                setDisplayDangerousGoodsSailing(displayDangerousGoodsSailing !== null ? displayDangerousGoodsSailing : event === null || event === void 0 ? void 0 : event.designatedDangerousGoodsSailing);\n                setTripEvent(event);\n                if (!tripReport_Stops) {\n                    var _event_dangerousGoodsRecords1, _event_stopLocation, _event_stopLocation1, _event_stopLocation2, _event_stopLocation3;\n                    setBufferDgr(event === null || event === void 0 ? void 0 : (_event_dangerousGoodsRecords1 = event.dangerousGoodsRecords) === null || _event_dangerousGoodsRecords1 === void 0 ? void 0 : _event_dangerousGoodsRecords1.nodes);\n                    setTripReport_Stops({\n                        geoLocationID: event.stopLocationID,\n                        arrTime: event === null || event === void 0 ? void 0 : event.arriveTime,\n                        depTime: event.departTime,\n                        paxOn: +event.paxJoined,\n                        paxOff: +event.paxDeparted,\n                        vehicleOn: +event.vehiclesJoined,\n                        vehicleOff: +event.vehiclesDeparted,\n                        otherCargo: event.otherCargo,\n                        comments: event.comments,\n                        lat: (_event_stopLocation = event.stopLocation) === null || _event_stopLocation === void 0 ? void 0 : _event_stopLocation.lat,\n                        long: (_event_stopLocation1 = event.stopLocation) === null || _event_stopLocation1 === void 0 ? void 0 : _event_stopLocation1.long,\n                        designatedDangerousGoodsSailing: event.designatedDangerousGoodsSailing\n                    });\n                    setArrTime(event.arriveTime);\n                    setDepTime(event.departTime);\n                    if (((_event_stopLocation2 = event.stopLocation) === null || _event_stopLocation2 === void 0 ? void 0 : _event_stopLocation2.lat) && ((_event_stopLocation3 = event.stopLocation) === null || _event_stopLocation3 === void 0 ? void 0 : _event_stopLocation3.long)) {\n                        var _event_stopLocation4, _event_stopLocation5;\n                        setCurrentLocation({\n                            latitude: (_event_stopLocation4 = event.stopLocation) === null || _event_stopLocation4 === void 0 ? void 0 : _event_stopLocation4.lat,\n                            longitude: (_event_stopLocation5 = event.stopLocation) === null || _event_stopLocation5 === void 0 ? void 0 : _event_stopLocation5.long\n                        });\n                    }\n                    if ((event === null || event === void 0 ? void 0 : event.lat) && (event === null || event === void 0 ? void 0 : event.long)) {\n                        setCurrentLocation({\n                            latitude: event === null || event === void 0 ? void 0 : event.lat,\n                            longitude: event === null || event === void 0 ? void 0 : event.long\n                        });\n                    }\n                }\n            }\n        },\n        onError: (error)=>{\n            console.error(\"Error getting current event\", error);\n        }\n    });\n    // Memoize locations array to prevent unnecessary re-creation\n    const memoizedLocations = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!geoLocations) return [];\n        return [\n            {\n                label: \"--- Add new location ---\",\n                value: \"newLocation\"\n            },\n            ...geoLocations.filter((location)=>location.title).map((location)=>({\n                    label: location.title,\n                    value: location.id,\n                    latitude: location.lat,\n                    longitude: location.long\n                }))\n        ];\n    }, [\n        geoLocations\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setLocations(memoizedLocations);\n    }, [\n        memoizedLocations\n    ]);\n    const validateForm = ()=>{\n        // Validate stopLocationID\n        const stopLocationID = +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID);\n        if (!stopLocationID || stopLocationID <= 0) {\n            toast({\n                title: \"Error\",\n                description: \"Please select a trip stop location\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        // Validate arriveTime - check both arrTime and tripReport_Stops.arriveTime/arrTime\n        const arriveTimeValue = arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime);\n        // Use isEmpty but also check for false value since arrTime's initial state is false\n        if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_3___default()(arriveTimeValue) || arriveTimeValue === false) {\n            toast({\n                title: \"Error\",\n                description: \"Please enter an arrival time\",\n                variant: \"destructive\"\n            });\n            return false;\n        }\n        return true;\n    };\n    const handleSave = async ()=>{\n        // Validate form before saving\n        if (!validateForm()) {\n            return;\n        }\n        // Use local state values for the most current input data\n        const paxOnValue = localPaxOn !== \"\" ? +localPaxOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOn) || 0;\n        const paxOffValue = localPaxOff !== \"\" ? +localPaxOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.paxOff) || 0;\n        const vehicleOnValue = localVehicleOn !== \"\" ? +localVehicleOn : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOn) || 0;\n        const vehicleOffValue = localVehicleOff !== \"\" ? +localVehicleOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.vehicleOff) || 0;\n        const variables = {\n            input: {\n                arriveTime: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime),\n                departTime: depTime ? depTime : tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.departTime,\n                paxJoined: paxOnValue,\n                paxDeparted: paxOffValue,\n                vehiclesJoined: isNaN(vehicleOnValue) ? 0 : vehicleOnValue,\n                vehiclesDeparted: isNaN(vehicleOffValue) ? 0 : vehicleOffValue,\n                stopLocationID: +(tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.geoLocationID),\n                otherCargo: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo,\n                comments: tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments,\n                lat: currentLocation.latitude.toString(),\n                long: currentLocation.longitude.toString(),\n                dangerousGoodsChecklistID: +(dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.id),\n                designatedDangerousGoodsSailing: displayDangerousGoodsSailing\n            }\n        };\n        if (currentEvent) {\n            if (offline) {\n                // updateTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                    ...variables.input\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n            } else {\n                updateTripReport_Stop({\n                    variables: {\n                        input: {\n                            id: +(selectedEvent === null || selectedEvent === void 0 ? void 0 : selectedEvent.id),\n                            ...variables.input\n                        }\n                    }\n                });\n            }\n        } else {\n            // Set default values for new records\n            variables.input.paxJoined = variables.input.paxJoined || 0;\n            variables.input.paxDeparted = variables.input.paxDeparted || 0;\n            if (offline) {\n                // createTripReport_Stop\n                const data = await tripReport_StopModel.save({\n                    ...variables.input,\n                    logBookEntrySectionID: currentTrip.id,\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)()\n                });\n                await getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n                if (bufferDgr.length > 0) {\n                    Promise.all(bufferDgr.map(async (dgr)=>{\n                        // updateDangerousGoodsRecord\n                        const dgrData = await dangerousGoodsRecordModel.save({\n                            id: dgr.id,\n                            tripReport_StopID: data.id,\n                            type: dgr.type,\n                            comment: dgr.comment\n                        });\n                        // Clear any existing toasts\n                        if ((currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0) {\n                            await getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id);\n                        } else {\n                            if (dgrData) {\n                                await getBufferDgr(dgrData.id);\n                            }\n                        }\n                        // createDangerousGoodsChecklist\n                        const dgChecklistData = await dangerousGoodsChecklistModel.save({\n                            id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                            tripReport_StopID: data.id,\n                            vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                            bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                            twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                            fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                            noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                            spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                            fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                            dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                            loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                            msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                            anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                            safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                            vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                        });\n                        setDgrChecklist(dgChecklistData);\n                    }));\n                }\n                updateTripReport({\n                    id: [\n                        ...tripReport.map((trip)=>trip.id),\n                        currentTrip.id\n                    ]\n                });\n                closeModal();\n            } else {\n                createTripReport_Stop({\n                    variables: {\n                        input: {\n                            ...variables.input,\n                            logBookEntrySectionID: currentTrip.id\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createDangerousGoodsChecklist] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateDangerousGoodsChecklist, {\n        onCompleted: (response)=>{\n            const data = response.createDangerousGoodsChecklist;\n            setDgrChecklist(data);\n        },\n        onError: (error)=>{\n            console.error(\"Error creating dangerous goods\", error);\n        }\n    });\n    const [updateDangerousGoodsRecord] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateDangerousGoodsRecord, {\n        onCompleted: (response)=>{\n            const data = response.updateDangerousGoodsRecord;\n            // Process the response\n            (currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) > 0 ? getCurrentTripReport_Stop(currentEvent === null || currentEvent === void 0 ? void 0 : currentEvent.id) : getBufferDgr(data.id);\n        },\n        onError: (error)=>{\n            console.error(\"Error updating dangerous goods record\", error);\n            toast({\n                title: \"Error\",\n                description: \"Error updating dangerous goods record\",\n                variant: \"destructive\"\n            });\n        }\n    });\n    const [createTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CreateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.createTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            if (bufferDgr.length > 0) {\n                bufferDgr.map((dgr)=>{\n                    updateDangerousGoodsRecord({\n                        variables: {\n                            input: {\n                                id: dgr.id,\n                                tripReport_StopID: data.id,\n                                type: dgr.type,\n                                comment: dgr.comment\n                            }\n                        }\n                    });\n                    createDangerousGoodsChecklist({\n                        variables: {\n                            input: {\n                                tripReport_StopID: data.id,\n                                vesselSecuredToWharf: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vesselSecuredToWharf,\n                                bravoFlagRaised: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.bravoFlagRaised,\n                                twoCrewLoadingVessel: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.twoCrewLoadingVessel,\n                                fireHosesRiggedAndReady: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireHosesRiggedAndReady,\n                                noSmokingSignagePosted: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.noSmokingSignagePosted,\n                                spillKitAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.spillKitAvailable,\n                                fireExtinguishersAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.fireExtinguishersAvailable,\n                                dgDeclarationReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.dgDeclarationReceived,\n                                loadPlanReceived: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.loadPlanReceived,\n                                msdsAvailable: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.msdsAvailable,\n                                anyVehiclesSecureToVehicleDeck: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.anyVehiclesSecureToVehicleDeck,\n                                safetyAnnouncement: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.safetyAnnouncement,\n                                vehicleStationaryAndSecure: dgrChecklist === null || dgrChecklist === void 0 ? void 0 : dgrChecklist.vehicleStationaryAndSecure\n                            }\n                        }\n                    });\n                });\n            }\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error creating passenger drop facility\", error);\n        }\n    });\n    const [updateTripReport_Stop] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.UpdateTripReport_Stop, {\n        onCompleted: (response)=>{\n            const data = response.updateTripReport_Stop;\n            getCurrentTripReport_Stop(data === null || data === void 0 ? void 0 : data.id);\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    currentTrip.id\n                ]\n            });\n            closeModal();\n        },\n        onError: (error)=>{\n            console.error(\"Error updating passenger drop facility\", error);\n        }\n    });\n    const handleCreateNewLocation = async ()=>{\n        const title = document.getElementById(\"new-location-title\");\n        const latitude = document.getElementById(\"new-location-latitude\");\n        const longitude = document.getElementById(\"new-location-longitude\");\n        if (title && latitude && longitude) {\n            if (offline) {\n                // createGeoLocation\n                const data = await geoLocationModel.save({\n                    id: (0,_app_offline_helpers_functions__WEBPACK_IMPORTED_MODULE_14__.generateUniqueId)(),\n                    title: title.value,\n                    lat: +latitude.value,\n                    long: +longitude.value,\n                    parentID: parentLocation\n                });\n                setLocations([\n                    ...locations,\n                    {\n                        label: data.title,\n                        value: data.id,\n                        latitude: data.lat,\n                        longitude: data.long\n                    }\n                ]);\n                setTripReport_Stops({\n                    ...tripReport_Stops,\n                    geoLocationID: data.id\n                });\n                setOpenNewLocationDialog(false);\n            } else {\n                createGeoLocation({\n                    variables: {\n                        input: {\n                            title: title.value,\n                            lat: +latitude.value,\n                            long: +longitude.value,\n                            parentID: parentLocation\n                        }\n                    }\n                });\n            }\n        }\n    };\n    const [createGeoLocation] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_4__.CREATE_GEO_LOCATION, {\n        onCompleted: (response)=>{\n            const data = response.createGeoLocation;\n            setLocations([\n                ...locations,\n                {\n                    label: data.title,\n                    value: data.id,\n                    latitude: data.lat,\n                    longitude: data.long\n                }\n            ]);\n            setTripReport_Stops({\n                ...tripReport_Stops,\n                geoLocationID: data.id\n            });\n            setOpenNewLocationDialog(false);\n        },\n        onError: (error)=>{\n            toast({\n                title: \"Error\",\n                description: \"Error creating GeoLocation: \" + error.message,\n                variant: \"destructive\"\n            });\n            setOpenNewLocationDialog(false);\n            console.error(\"Error creating new location\", error);\n        }\n    });\n    var _tripReport_Stops_depTime;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.P, {\n                className: \"max-w-[40rem] leading-loose\",\n                children: \"For recording trip stops where passengers, cargo and/or vehicles maybe getting on and off.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 928,\n                columnNumber: 13\n            }, this),\n            displayField(type + \"Location\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Location of trip stop\",\n                htmlFor: \"trip-location\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_location__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    offline: offline,\n                    setCurrentLocation: setCurrentLocation,\n                    handleLocationChange: handleLocationChangeCallback,\n                    currentEvent: memoizedCurrentEvent\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 937,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 933,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Arrival\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Arrival Time\",\n                htmlFor: \"arrival-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: arrTime || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arriveTime) || (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.arrTime) || \"\",\n                    handleTimeChange: handleArrTimeChange,\n                    timeID: \"arrival-time\",\n                    fieldName: \"Arrival Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 950,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 946,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"Departure\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Departure Time\",\n                htmlFor: \"departure-time\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_time__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    time: depTime ? depTime : (_tripReport_Stops_depTime = tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.depTime) !== null && _tripReport_Stops_depTime !== void 0 ? _tripReport_Stops_depTime : \"\",\n                    handleTimeChange: handleDepTimeChange,\n                    timeID: \"departure-time\",\n                    fieldName: \"Departure Time\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 968,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 964,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"PaxPickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers off\",\n                        htmlFor: \"paxOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOff\",\n                            name: \"paxOff\",\n                            type: \"number\",\n                            value: localPaxOff,\n                            placeholder: \"Pax off\",\n                            min: \"0\",\n                            onChange: handlePaxOffChange,\n                            onBlur: handlePaxOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 985,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 981,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Passengers on\",\n                        htmlFor: \"paxOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"paxOn\",\n                            name: \"paxOn\",\n                            type: \"number\",\n                            value: localPaxOn,\n                            placeholder: \"Pax on\",\n                            min: \"0\",\n                            onChange: handlePaxOnChange,\n                            onBlur: handlePaxOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1000,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 996,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 979,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"VehiclePickDrop\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4 flex flex-row gap-4 w-full\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles on\",\n                        htmlFor: \"vehicleOn\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOn\",\n                            name: \"vehicleOn\",\n                            type: \"number\",\n                            value: localVehicleOn,\n                            placeholder: \"Vehicles getting on\",\n                            min: \"0\",\n                            onChange: handleVehicleOnChange,\n                            onBlur: handleVehicleOnBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1020,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1016,\n                        columnNumber: 21\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                        label: \"Vehicles off\",\n                        htmlFor: \"vehicleOff\",\n                        className: \"w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"vehicleOff\",\n                            name: \"vehicleOff\",\n                            type: \"number\",\n                            value: localVehicleOff,\n                            placeholder: \"Vehicles getting off\",\n                            min: \"0\",\n                            onChange: handleVehicleOffChange,\n                            onBlur: handleVehicleOffBlur\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1035,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1031,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1014,\n                columnNumber: 17\n            }, this),\n            displayField(type + \"OtherCargo\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Cargo (if any)\",\n                htmlFor: \"cargo-onOff\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"cargo-onOff\",\n                    placeholder: \"Other cargo on and off\",\n                    value: cargoOnOff !== \"\" ? cargoOnOff : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.otherCargo) || \"\",\n                    onChange: (e)=>{\n                        setCargoOnOff(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            otherCargo: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1053,\n                    columnNumber: 21\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1049,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pvpddgr__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                offline: offline,\n                locked: locked,\n                currentTrip: currentTrip,\n                logBookConfig: logBookConfig,\n                selectedDGR: selectedDGR,\n                setSelectedDGR: setSelectedDGR,\n                members: members,\n                displayDangerousGoods: displayDangerousGoods,\n                setDisplayDangerousGoods: setDisplayDangerousGoods,\n                displayDangerousGoodsSailing: displayDangerousGoodsSailing,\n                setDisplayDangerousGoodsSailing: setDisplayDangerousGoodsSailing,\n                allDangerousGoods: allPVPDDangerousGoods,\n                setAllDangerousGoods: setAllPVPDDangerousGoods,\n                currentEvent: tripEvent\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1074,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_16__.Label, {\n                label: \"Comments\",\n                htmlFor: \"comments\",\n                className: \"\".concat(locked ? \"pointer-events-none\" : \"\", \" my-4\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_17__.Textarea, {\n                    id: \"comments\",\n                    placeholder: \"Comments\",\n                    value: comments !== \"\" ? comments : (tripReport_Stops === null || tripReport_Stops === void 0 ? void 0 : tripReport_Stops.comments) || \"\",\n                    onChange: (e)=>{\n                        setComments(e.target.value);\n                    },\n                    onBlur: (e)=>{\n                        setTripReport_Stops({\n                            ...tripReport_Stops,\n                            comments: e.target.value\n                        });\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                    lineNumber: 1097,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1093,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"back\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                        onClick: ()=>closeModal(),\n                        children: \"Cancel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1117,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_18__.Button, {\n                        variant: \"primary\",\n                        iconLeft: _barrel_optimize_names_ArrowLeft_Check_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                        onClick: locked ? ()=>{} : handleSave,\n                        disabled: locked,\n                        children: selectedEvent ? \"Update\" : \"Save\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1123,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1116,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_20__.AlertDialogNew, {\n                openDialog: openNewLocationDialog,\n                setOpenDialog: setOpenNewLocationDialog,\n                actionText: \"Add New Location\",\n                handleCreate: handleCreateNewLocation,\n                title: \"Add New Location\",\n                description: \"Create a new location for trip stops\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"my-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-title\",\n                            type: \"text\",\n                            \"aria-describedby\": \"title-error\",\n                            required: true,\n                            placeholder: \"Location Title\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1139,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1138,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_comboBox__WEBPACK_IMPORTED_MODULE_19__.Combobox, {\n                            options: locations || [],\n                            onChange: handleParentLocationChangeCallback,\n                            placeholder: \"Parent Location (Optional)\",\n                            buttonClassName: \"w-full\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1148,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1147,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-latitude\",\n                            type: \"text\",\n                            defaultValue: location.latitude,\n                            \"aria-describedby\": \"latitude-error\",\n                            required: true,\n                            placeholder: \"Latitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1156,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1155,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_15__.Input, {\n                            id: \"new-location-longitude\",\n                            type: \"text\",\n                            defaultValue: location.longitude,\n                            \"aria-describedby\": \"longitude-error\",\n                            required: true,\n                            placeholder: \"Longitude\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                            lineNumber: 1166,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                        lineNumber: 1165,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n                lineNumber: 1131,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\forms\\\\passenger-vehicle-pick-drop.tsx\",\n        lineNumber: 927,\n        columnNumber: 9\n    }, this);\n}\n_s(PassengerVehiclePickDrop, \"FcV64c9PeXf8lTDZUaoOswTKPBM=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_21__.useLazyQuery,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_22__.useMutation\n    ];\n});\n_c = PassengerVehiclePickDrop;\nvar _c;\n$RefreshReg$(_c, \"PassengerVehiclePickDrop\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/forms/passenger-vehicle-pick-drop.tsx\n"));

/***/ })

});