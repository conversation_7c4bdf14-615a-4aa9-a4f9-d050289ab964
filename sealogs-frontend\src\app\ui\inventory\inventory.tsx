'use client'
import React, { useEffect, useState } from 'react'
import { useLazyQuery, useMutation } from '@apollo/client'
import {
    GET_CREW_BY_IDS,
    GET_MAINTENANCE_CHECK_LIST,
} from '@/app/lib/graphQL/query'
import Editor from '@/app/ui/editor'
import FileUpload from '@/components/file-upload'
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
    CardFooter,
} from '@/components/ui/card'
import {
    UPDATE_INVENTORY,
    CREATE_INVENTORY_CATEGORY,
    DELETE_INVENTORIES,
    CREATE_SUPPLIER,
    CREATE_SEALOGS_FILE_LINKS,
} from '@/app/lib/graphQL/mutation'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { InputSkeleton, List, TableSkeleton } from '@/components/skeletons'
import {
    getInventoryByID,
    getVesselList,
    getSupplier,
    getInventoryCategory,
} from '@/app/lib/actions'

import FileItem from '@/components/file-item'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import Loading from '@/app/loading'
import { Input } from '@/components/ui/input'
import { Combobox } from '@/components/ui/comboBox'
import { Textarea } from '@/components/ui/textarea'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { FooterWrapper } from '@/components/footer-wrapper'
import { AlertDialogNew, H3, Label } from '@/components/ui'
import { MaintenanceTable } from '../maintenance/list/list'
import { nullable } from 'zod'
import { ArrowLeft, Delete, Trash, X } from 'lucide-react'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'
import { getResponsiveLabel } from '../../../../utils/responsiveLabel'
import { useToast } from '@/hooks/use-toast'
import CloudFlareCaptures from '../logbook/components/CloudFlareCaptures'

export default function Inventory({
    inventoryID,
    inventoryTab = '',
}: {
    inventoryID: number
    inventoryTab: string
}) {
    const searchParams = useSearchParams()
    const [activeTab, setActiveTab] = useState('info')
    const [inventory, setInventory] = useState<any>()
    const [categories, setCategories] = useState<any>()
    const [selectedCategories, setSelectedCategories] = useState<any>()
    const [suppliers, setSuppliers] = useState<any>()
    const [selectedSuppliers, setSelectedSuppliers] = useState<any>()
    const [selectedLocation, setSelectedLocation] = useState<any>()
    //    const [attachments, setAttachments] = useState<any>()
    const [vessels, setVessels] = useState<any>()
    const [tasks, setTasks] = useState<any>()
    const [taskCounter, setTaskCounter] = useState<number>(0)
    const [displayTask, setDisplayTask] = useState(false)
    const [crewInfo, setCrewInfo] = useState<any>()
    const [openLocationDialog, setOpenLocationDialog] = useState(false)
    const [openSupplierDialog, setOpenSupplierDialog] = useState(false)
    const [openCategoryDialog, setOpenCategoryDialog] = useState(false)
    const [
        openConfirmInventoryDeleteDialog,
        setOpenConfirmInventoryDeleteDialog,
    ] = useState(false)
    const [documents, setDocuments] = useState<Array<Record<string, any>>>([])
    const [fileLinks, setFileLinks] = useState<any>([])
    const [linkSelectedOption, setLinkSelectedOption] = useState<any>([])
    const router = useRouter()

    const [permissions, setPermissions] = useState<any>(false)
    const [edit_task, setEdit_task] = useState<any>(false)
    const [edit_inventory, setEdit_inventory] = useState<any>(false)
    const [delete_inventory, setDelete_inventory] = useState<any>(false)
    const [view_inventory, setView_inventory] = useState<any>(false)
    const [description, setDescription] = useState<any>(false)
    const { getVesselWithIcon } = useVesselIconData()
    const bp = useBreakpoints()

    const { toast } = useToast()

    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('EDIT_TASK', permissions)) {
                setEdit_task(true)
            } else {
                setEdit_task(false)
            }
            if (hasPermission('EDIT_INVENTORY', permissions)) {
                setEdit_inventory(true)
            } else {
                setEdit_inventory(false)
            }
            if (hasPermission('DELETE_INVENTORY', permissions)) {
                setDelete_inventory(true)
            } else {
                setDelete_inventory(false)
            }
            if (hasPermission('VIEW_INVENTORY', permissions)) {
                setView_inventory(true)
            } else {
                setView_inventory(false)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    const handleSetInventory = (data: any) => {
        const getContent = () => {
            const content = data.content !== 'null' ? data.content ?? '' : ''
            const description =
                data.description !== 'null' ? data.description ?? '' : ''

            return `${content} ${description}`.trim()
        }
        setInventory({
            ...data,
            content: getContent(),
        })
        if (inventoryTab === 'maintenance') {
            setDisplayTask(true)
        }
        setSelectedLocation({ label: data?.location, value: 0 })
        setSelectedCategories(
            data?.categories?.nodes?.map((category: any) => ({
                label: category.name,
                value: category.id,
            })),
        )
        setSelectedSuppliers(
            data?.suppliers?.nodes?.map((supplier: any) => ({
                label: supplier.name,
                value: supplier.id,
            })),
        )
        queryMaintenanceCheck({
            variables: {
                inventoryID: +inventoryID,
                vesselID: 0,
            },
        })
        setDocuments(data?.documents?.nodes)
        setLinkSelectedOption(
            data?.attachmentLinks?.nodes.map((link: any) => ({
                label: link.link,
                value: link.id,
            })),
        )
    }

    getInventoryByID(inventoryID, handleSetInventory)

    const handleSetVessels = (vessels: any) => {
        const activeVessels = vessels.filter((vessel: any) => !vessel.archived)
        const vesselList = activeVessels.map((item: any) => ({
            ...item,
        }))
        const appendedData = [
            // { title: '-- Other --', id: 'newLocation' },
            ...vesselList,
            { title: 'Other', id: '0' },
        ]
        setVessels(appendedData)
    }

    getVesselList(handleSetVessels)

    const handelSetSuppliers = (data: any) => {
        const suppliersList = [
            {
                label: ' ---- Create supplier ---- ',
                value: 'newSupplier',
            },
            ...data
                ?.filter((supplier: any) => supplier.name !== null)
                .map((supplier: any) => ({
                    label: supplier.name,
                    value: supplier.id,
                })),
        ]
        setSuppliers(suppliersList)
    }

    getSupplier(handelSetSuppliers)

    const handleSetCategories = (data: any) => {
        const formattedData = [
            {
                label: ' ---- Create Category ---- ',
                value: 'newCategory',
            },
            ...data
                ?.filter(
                    (category: any) =>
                        category.name !== null && category.archived === false,
                )
                .map((category: any) => ({
                    label: category.name,
                    value: category.id,
                })),
        ]
        setCategories(formattedData)
    }

    getInventoryCategory(handleSetCategories)

    const [queryMaintenanceCheck] = useLazyQuery(GET_MAINTENANCE_CHECK_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readComponentMaintenanceCheckList[0].list
            if (data) {
                const activeTasks = data.filter(
                    (task: any) => task?.archived != 1,
                )
                setTasks(data)
                const taskCounter = activeTasks.filter(
                    (task: any) => task.isOverDue.status === 'High',
                ).length
                setTaskCounter(taskCounter)
                const appendedData: number[] = Array.from(
                    new Set(
                        data
                            .filter((item: any) => item.assignedToID > 0)
                            .map((item: any) => item.assignedToID),
                    ),
                )
                loadCrewMemberInfo(appendedData)
            }
        },
        onError: (error: any) => {
            console.error('queryMaintenanceCheck error', error)
        },
    })

    const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readSeaLogsMembers.nodes
            if (data) {
                setCrewInfo(data)
            }
        },
        onError: (error) => {
            console.error('queryCrewMemberInfo error', error)
        },
    })

    const loadCrewMemberInfo = async (crewId: any) => {
        await queryCrewMemberInfo({
            variables: {
                crewMemberIDs: crewId.length > 0 ? crewId : [0],
            },
        })
    }

    const handleSetSelectedCategories = (selectedOption: any) => {
        if (
            selectedOption.find((option: any) => option.value === 'newCategory')
        ) {
            setOpenCategoryDialog(true)
        }
        setSelectedCategories(
            selectedOption.filter(
                (option: any) => option.value !== 'newCategory',
            ),
        )
    }

    const handleEditorChange = (content: any) => {
        setInventory({ ...inventory, content: content })
    }

    const handleSave = async () => {
        if (!inventory) {
            console.error(
                'Inventory page has not been initialised, possibly a slow internet connection, please try after a few seconds',
            )
            toast({
                description:
                    'Please wait to initialize the inventory before saving',
                variant: 'destructive',
            })
            return
        }
        if (!edit_inventory) {
            toast({
                description:
                    'You do not have permission to edit this inventory',
                variant: 'destructive',
            })
            return
        }
        const variables = {
            input: {
                id: +inventory.id,
                item: (
                    document.getElementById(
                        'inventory-name',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-name',
                          ) as HTMLInputElement
                      ).value
                    : inventory.item,
                title: (
                    document.getElementById(
                        'inventory-name',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-name',
                          ) as HTMLInputElement
                      ).value
                    : inventory.title,
                location: (
                    document.getElementById(
                        'inventory-location',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-location',
                          ) as HTMLInputElement
                      ).value
                    : inventory.location,
                description: null,
                content: inventory.content,
                quantity: (
                    document.getElementById('inventory-qty') as HTMLInputElement
                ).value
                    ? parseInt(
                          (
                              document.getElementById(
                                  'inventory-qty',
                              ) as HTMLInputElement
                          ).value,
                      )
                    : inventory.quantity,
                productCode: (
                    document.getElementById(
                        'inventory-code',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-code',
                          ) as HTMLInputElement
                      ).value
                    : inventory.productCode,
                costingDetails: (
                    document.getElementById(
                        'inventory-cost',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-cost',
                          ) as HTMLInputElement
                      ).value
                    : inventory.costingDetails,
                comments: (
                    document.getElementById(
                        'inventory-comments',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-comments',
                          ) as HTMLInputElement
                      ).value
                    : inventory.comments,
                archived: inventory.archived,
                inventoryImportID: inventory.inventoryImportID,
                vesselID: selectedLocation.value
                    ? selectedLocation.value
                    : inventory.vesselID,
                // attachments: inventory.attachments,
                documents: documents.map((doc: any) => doc.id).join(','),
                categories: selectedCategories?.map(
                    (category: any) => category.value,
                ).length
                    ? selectedCategories
                          .map((category: any) => category.value)
                          .join(',')
                    : inventory.categories.nodes
                          .map((categories: any) => categories.id)
                          .join(','),
                suppliers: selectedSuppliers?.map(
                    (supplier: any) => supplier.value,
                ).length
                    ? selectedSuppliers
                          .map((supplier: any) => supplier.value)
                          .join(',')
                    : inventory.suppliers.nodes
                          .map((supplier: any) => supplier.id)
                          .join(','),
                attachmentLinks: linkSelectedOption
                    ? linkSelectedOption
                          .map((link: any) => link.value)
                          .join(',')
                    : inventory.attachmentLinks?.nodes
                          .map((link: any) => link.id)
                          .join(','),
            },
        }
        await mutationUpdateInventory({
            variables,
        })
    }

    const [
        mutationUpdateInventory,
        { loading: mutationupdateInventoryLoading },
    ] = useMutation(UPDATE_INVENTORY, {
        onCompleted: (response: any) => {
            const data = response.updateInventory
            if (data.id > 0) {
                searchParams.get('redirect_to')
                    ? router.push(searchParams?.get('redirect_to') + '')
                    : router.back()
            } else {
                console.error('mutationupdateInventory error', response)
            }
        },
        onError: (error: any) => {
            console.error('mutationupdateInventory error', error)
        },
    })

    const handleCreateCategory = async () => {
        const categoryName = (
            document.getElementById(
                'inventory-new-category',
            ) as HTMLInputElement
        ).value
        return await mutationcreateInventoryCategory({
            variables: {
                input: {
                    name: categoryName,
                },
            },
        })
    }

    const [
        mutationcreateInventoryCategory,
        { loading: mutationcreateInventoryCategoryLoading },
    ] = useMutation(CREATE_INVENTORY_CATEGORY, {
        onCompleted: (response: any) => {
            const data = response.createInventoryCategory
            if (data.id > 0) {
                const formattedData = [
                    ...categories,
                    { label: data.name, value: data.id },
                ]
                setCategories(formattedData)
                const categoriesList = [
                    ...selectedCategories,
                    { label: data.name, value: data.id },
                ]
                setSelectedCategories(categoriesList)
                setOpenCategoryDialog(false)
            } else {
                console.error('mutationcreateInventoryCategory error', response)
            }
        },
        onError: (error: any) => {
            console.error('mutationcreateInventoryCategory error', error)
        },
    })

    const handleDeleteInventories = async () => {
        if (!delete_inventory) {
            toast({
                description:
                    'You do not have permission to delete this inventory',
                variant: 'destructive',
            })
            return
        }
        await mutationDeleteInventories({
            variables: {
                ids: [+inventory.id],
            },
        })
    }

    const [
        mutationDeleteInventories,
        { loading: mutationdeleteInventoriesLoading },
    ] = useMutation(DELETE_INVENTORIES, {
        onCompleted: (response: any) => {
            if (
                response.deleteInventories &&
                response.deleteInventories.length > 0
            ) {
                router.push('/inventory')
            } else {
                console.error(
                    'mutationdeleteInventories failed to delete:',
                    response,
                )
            }
        },
        onError: (error: any) => {
            console.error('mutationdeleteInventories error:', error.message)
        },
    })

    const handleSelectedVesselChange = (selectedOption: any) => {
        if (selectedOption && selectedOption.value === 'newLocation') {
            setOpenLocationDialog(true)
        }
        setSelectedLocation(selectedOption)
    }

    const handleCreateLocation = (Location: any) => {
        var newLocation = { label: '', value: '' }
        if (typeof Location === 'string') {
            newLocation = { label: Location, value: Location }
        }
        if (typeof Location === 'object') {
            newLocation = {
                label: (
                    document.getElementById(
                        'inventory-new-location',
                    ) as HTMLInputElement
                ).value,
                value: (
                    document.getElementById(
                        'inventory-new-location-id',
                    ) as HTMLInputElement
                ).value
                    ? (
                          document.getElementById(
                              'inventory-new-location-id',
                          ) as HTMLInputElement
                      ).value
                    : (
                          document.getElementById(
                              'inventory-new-location',
                          ) as HTMLInputElement
                      ).value,
            }
        }
        const vesselList = vessels.map((item: any) => ({
            ...item,
        }))
        const appendedData = [
            ...vesselList,
            { Title: newLocation.label, ID: newLocation.value },
        ]
        setVessels(appendedData)
        setSelectedLocation(newLocation)
        setOpenLocationDialog(false)
    }

    const deleteFile = async (id: number) => {
        const newDocuments = documents.filter((doc: any) => doc.id !== id)
        setDocuments(newDocuments)
    }

    const handleDisplayTask = () => {
        setDisplayTask(true)
    }

    const handleSelectedSuppliers = (selectedOption: any) => {
        if (
            selectedOption.find((option: any) => option.value === 'newSupplier')
        ) {
            setOpenSupplierDialog(true)
        }
        setSelectedSuppliers(
            selectedOption.filter(
                (option: any) => option.value !== 'newSupplier',
            ),
        )
    }

    const handleCreateSupplier = async () => {
        const name = (
            document.getElementById('supplier-name') as HTMLInputElement
        ).value
        const website = (
            document.getElementById('supplier-website') as HTMLInputElement
        ).value
        const phone = (
            document.getElementById('supplier-phone') as HTMLInputElement
        ).value
        const email = (
            document.getElementById('supplier-email') as HTMLInputElement
        ).value
        const address = (
            document.getElementById('supplier-address') as HTMLInputElement
        ).value

        const variables = {
            input: {
                name: name,
                address: address,
                website: website,
                email: email,
                phone: phone,
            },
        }
        if (name !== '') {
            await mutationCreateSupplier({
                variables,
            })
        }
        setOpenSupplierDialog(false)
    }

    const [mutationCreateSupplier, { loading: mutationcreateSupplierLoading }] =
        useMutation(CREATE_SUPPLIER, {
            onCompleted: (response: any) => {
                const data = response.createSupplier
                if (data.id > 0) {
                    const suppliersList = [
                        ...suppliers,
                        { label: data.name, value: data.id },
                    ]
                    setSuppliers(suppliersList)
                    const selectedSuppliersList = [
                        ...selectedSuppliers,
                        { label: data.name, value: data.id },
                    ]
                    setSelectedSuppliers(selectedSuppliersList)
                } else {
                    console.error('mutationcreateSupplier error', response)
                }
            },
            onError: (error: any) => {
                console.error('mutationcreateSupplier error', error)
            },
        })

    const [createSeaLogsFileLinks] = useMutation(CREATE_SEALOGS_FILE_LINKS, {
        onCompleted: (response: any) => {
            const data = response.createSeaLogsFileLinks
            if (data.id > 0) {
                const newLinks = [...fileLinks, data]
                setFileLinks(newLinks)
                linkSelectedOption
                    ? setLinkSelectedOption([
                          ...linkSelectedOption,
                          { label: data.link, value: data.id },
                      ])
                    : setLinkSelectedOption([
                          { label: data.link, value: data.id },
                      ])
            }
        },
        onError: (error: any) => {
            console.error('createSeaLogsFileLinksEntry error', error)
        },
    })

    const handleDeleteLink = (link: any) => {
        setLinkSelectedOption(linkSelectedOption.filter((l: any) => l !== link))
    }

    const linkItem = (link: any) => {
        if (!link.label) {
            return null
        }
        return (
            <div className="flex justify-between align-middle mr-2 w-fit">
                <Link href={link.label} target="_blank" className="ml-2 ">
                    {link.label}
                </Link>
                <div className="ml-2 ">
                    <Button
                        variant="destructive"
                        iconOnly
                        iconLeft={X}
                        onClick={() => handleDeleteLink(link)}
                    />
                </div>
            </div>
        )
    }

    if (!permissions || !view_inventory) {
        return !permissions ? (
            <Loading />
        ) : (
            <Loading errorMessage="Oops You do not have the permission to view this section." />
        )
    }

    const confirmInventoryCrew = () => {
        if (inventory) {
            setOpenConfirmInventoryDeleteDialog(true)
        }
    }

    return (
        <>
            <Card>
                <CardHeader>
                    <CardTitle className="text-2xl font-medium">
                        <span className="text-muted-foreground mr-2">
                            Inventory:
                        </span>
                        {inventory?.item}
                    </CardTitle>
                </CardHeader>
                <Separator className="mb-3" />
                <CardContent>
                    <Tabs
                        defaultValue="info"
                        value={activeTab}
                        onValueChange={setActiveTab}>
                        <TabsList>
                            <TabsTrigger value="info">Item Info</TabsTrigger>
                            <TabsTrigger value="tasks" className="relative">
                                Tasks / Maintenance
                                {taskCounter > 0 && (
                                    <Badge
                                        variant="outline"
                                        className={`ml-2 h-4 ${taskCounter > 0 ? 'bg-rose-100 text-rose-700 hover:bg-rose-200' : 'bg-emerald-100 text-emerald-700 hover:bg-emerald-200'}`}>
                                        {taskCounter}
                                    </Badge>
                                )}
                            </TabsTrigger>
                        </TabsList>

                        <TabsContent value="tasks" className="pt-4">
                            <div className="w-full">
                                {tasks && vessels ? (
                                    <MaintenanceTable
                                        maintenanceChecks={tasks}
                                        vessels={vessels}
                                        crewInfo={crewInfo}
                                    />
                                ) : (
                                    <TableSkeleton />
                                )}
                            </div>
                        </TabsContent>

                        <TabsContent value="info">
                            <div className="space-y-6 py-4">
                                {/* Basic Info Section */}
                                <div className="space-y-4">
                                    <div className="grid sm:grid-cols-2 gap-4">
                                        <Input
                                            id="inventory-name"
                                            type="text"
                                            defaultValue={inventory?.item}
                                            placeholder="Inventory name"
                                            readOnly={!edit_inventory}
                                        />
                                        {vessels && inventory ? (
                                            <Combobox
                                                id="inventory-vessel"
                                                options={vessels?.map(
                                                    (vessel: any) => {
                                                        const vesselWithIcon =
                                                            getVesselWithIcon(
                                                                vessel.id,
                                                                vessel,
                                                            )
                                                        return {
                                                            label: vessel.title,
                                                            value: vessel.id,
                                                            vessel: vesselWithIcon,
                                                        }
                                                    },
                                                )}
                                                isDisabled={!edit_inventory}
                                                defaultValues={
                                                    inventory?.vesselID &&
                                                    inventory?.vesselID == 0
                                                        ? {
                                                              label: 'Other',
                                                              value: '0',
                                                          }
                                                        : inventory?.vessel
                                                          ? {
                                                                label: inventory
                                                                    .vessel
                                                                    .title,
                                                                value: inventory
                                                                    .vessel.id,
                                                                vessel: getVesselWithIcon(
                                                                    inventory
                                                                        .vessel
                                                                        .id,
                                                                    inventory.vessel,
                                                                ),
                                                            }
                                                          : null
                                                }
                                                className="w-full"
                                                placeholder="Select Vessel"
                                                onChange={
                                                    handleSelectedVesselChange
                                                }
                                            />
                                        ) : (
                                            <InputSkeleton />
                                        )}
                                    </div>

                                    <div className="grid sm:grid-cols-2 gap-4">
                                        <Input
                                            id="inventory-location"
                                            type="text"
                                            defaultValue={inventory?.location}
                                            placeholder="Location"
                                            readOnly={!edit_inventory}
                                        />

                                        <Input
                                            id="inventory-qty"
                                            type="number"
                                            defaultValue={inventory?.quantity}
                                            placeholder="Quantity"
                                            readOnly={!edit_inventory}
                                        />
                                    </div>
                                </div>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div className="col-span-1">
                                        <h3 className="text-lg font-medium mb-2">
                                            Description
                                        </h3>
                                        <p className="text-muted-foreground text-sm leading-relaxed">
                                            Enter details that might help with
                                            the maintenance or operation of this
                                            item.
                                        </p>
                                    </div>
                                    <div className="col-span-2 space-y-4">
                                        {inventory && (
                                            <Editor
                                                id="inventory-Content"
                                                content={inventory?.content}
                                                handleEditorChange={
                                                    handleEditorChange
                                                }
                                            />
                                        )}
                                    </div>
                                </div>

                                <Separator />

                                {/* Inventory Details Section */}
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div className="col-span-1">
                                        <h3 className="text-lg font-medium mb-2">
                                            Inventory details
                                        </h3>
                                        <p className="text-muted-foreground text-sm leading-relaxed">
                                            In this section categorise the item
                                            and add the suppliers where you
                                            normally purchase this item and the
                                            expected cost. This will help
                                            replacing the item in the future.
                                        </p>
                                    </div>
                                    <div className="col-span-2">
                                        <Card className="space-y-5">
                                            <Label
                                                label="Product code"
                                                htmlFor="inventory-code">
                                                <Input
                                                    id="inventory-code"
                                                    type="text"
                                                    defaultValue={
                                                        inventory?.productCode
                                                    }
                                                    placeholder="Product code"
                                                    readOnly={!edit_inventory}
                                                />
                                            </Label>
                                            {inventory && categories ? (
                                                <Combobox
                                                    className="w-full"
                                                    label="Categories"
                                                    id="inventory-categories"
                                                    defaultValues={
                                                        inventory.categories &&
                                                        inventory.categories.nodes.map(
                                                            (
                                                                category: any,
                                                            ) => ({
                                                                label: category.name,
                                                                value: category.id,
                                                            }),
                                                        )
                                                    }
                                                    isDisabled={!edit_inventory}
                                                    value={selectedCategories}
                                                    multi
                                                    options={categories}
                                                    onChange={
                                                        handleSetSelectedCategories
                                                    }
                                                />
                                            ) : (
                                                <InputSkeleton />
                                            )}

                                            {inventory && suppliers ? (
                                                <Combobox
                                                    label="Supplier"
                                                    className="w-full"
                                                    id="inventory-suppliers"
                                                    defaultValues={
                                                        inventory.Suppliers &&
                                                        suppliers
                                                            ?.filter(
                                                                (
                                                                    supplier: any,
                                                                ) =>
                                                                    inventory?.Suppliers &&
                                                                    Object.keys(
                                                                        inventory.Suppliers,
                                                                    ).includes(
                                                                        supplier?.value?.toString(),
                                                                    ),
                                                            )
                                                            .map(
                                                                (
                                                                    supplier: any,
                                                                ) => ({
                                                                    label: supplier.label,
                                                                    value: supplier.value,
                                                                }),
                                                            )
                                                    }
                                                    multi
                                                    isDisabled={!edit_inventory}
                                                    value={selectedSuppliers}
                                                    onChange={
                                                        handleSelectedSuppliers
                                                    }
                                                    options={suppliers}
                                                />
                                            ) : (
                                                <InputSkeleton />
                                            )}

                                            <Label
                                                label="Cost"
                                                htmlFor="inventory-cost">
                                                <Input
                                                    id="inventory-cost"
                                                    type="text"
                                                    defaultValue={
                                                        inventory?.costingDetails
                                                    }
                                                    placeholder="Costing details"
                                                    readOnly={!edit_inventory}
                                                />
                                            </Label>
                                            <div className="space-y-3">
                                                <Label
                                                    label="Links"
                                                    htmlFor="task-title">
                                                    <Input
                                                        id="task-title"
                                                        type="text"
                                                        placeholder="Type a link and press Enter"
                                                        readOnly={
                                                            !edit_inventory
                                                        }
                                                        onKeyDown={async (
                                                            event: React.KeyboardEvent<HTMLInputElement>,
                                                        ) => {
                                                            if (
                                                                event.key ===
                                                                'Enter'
                                                            ) {
                                                                const inputValue =
                                                                    (
                                                                        event.target as HTMLInputElement
                                                                    ).value
                                                                await createSeaLogsFileLinks(
                                                                    {
                                                                        variables:
                                                                            {
                                                                                input: {
                                                                                    link: inputValue,
                                                                                },
                                                                            },
                                                                    },
                                                                )
                                                                ;(
                                                                    event.target as HTMLInputElement
                                                                ).value = ''
                                                            }
                                                        }}
                                                    />
                                                </Label>

                                                <div className="flex flex-wrap gap-2">
                                                    {linkSelectedOption
                                                        ? linkSelectedOption.map(
                                                              (link: any) => (
                                                                  <div
                                                                      key={
                                                                          link.value
                                                                      }>
                                                                      {linkItem(
                                                                          link,
                                                                      )}
                                                                  </div>
                                                              ),
                                                          )
                                                        : fileLinks.map(
                                                              (link: any) => (
                                                                  <div
                                                                      key={
                                                                          link.value
                                                                      }>
                                                                      {linkItem(
                                                                          link,
                                                                      )}
                                                                  </div>
                                                              ),
                                                          )}
                                                </div>
                                            </div>
                                        </Card>
                                    </div>
                                </div>

                                <Separator />

                                {/* Attachments Section */}
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div className="col-span-1">
                                        <h3 className="text-lg font-medium mb-2">
                                            Attachment
                                        </h3>
                                        <p className="text-muted-foreground text-sm leading-relaxed">
                                            Upload things like photos of the
                                            item, plus warranty and guarantee
                                            documents or operating manuals. Add
                                            links to any online manuals or
                                            product descriptions.
                                        </p>
                                    </div>
                                    <div className="col-span-2 grid grid-cols-1 md:grid-cols-2 gap-6">
                                        {/* {edit_inventory && (
                                            // <div className="col-span-1">
                                            //     <FileUpload
                                            //         setDocuments={setDocuments}
                                            //         text=""
                                            //         subText="Drag files here or upload"
                                            //         bgClass=""
                                            //         documents={documents}
                                            //     />
                                            // </div>
                                        // )} */}
                                        <div className="col-span-2 flex items-end">
                                            <div className="w-full flex flex-col space-y-2">
                                                <CloudFlareCaptures
                                                    inputId={1}
                                                    sectionId={inventoryID}
                                                    buttonType={'button'}
                                                    sectionName={'inventoryID'}
                                                    editable={edit_inventory}
                                                />
                                            </div>
                                            {/* {documents.length > 0 && (
                                                <div className="space-y-2">
                                                    {documents.map(
                                                        (document: any) => (
                                                            <div
                                                                className="p-2.5"
                                                                key={
                                                                    document.id
                                                                }>
                                                                <FileItem
                                                                    document={
                                                                        document
                                                                    }
                                                                    showDeleteButton={
                                                                        true
                                                                    }
                                                                    canDelete={
                                                                        edit_inventory
                                                                    }
                                                                    onDelete={(
                                                                        documentId,
                                                                    ) =>
                                                                        deleteFile(
                                                                            parseInt(
                                                                                documentId,
                                                                            ),
                                                                        )
                                                                    }
                                                                    deleteErrorMessage="You do not have permission to delete this document"
                                                                />
                                                            </div>
                                                        ),
                                                    )}
                                                </div>
                                            )} */}
                                        </div>
                                    </div>
                                </div>

                                <Separator />

                                {/* Description Section */}
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div className="col-span-1">
                                        <h3 className="text-lg font-medium mb-2">
                                            Comment
                                        </h3>
                                        <p className="text-muted-foreground text-sm leading-relaxed">
                                            Comments are sent to directly to a
                                            person (use @name) to send a comment
                                            to someone.
                                        </p>
                                    </div>
                                    <div className="col-span-2 space-y-4">
                                        <Textarea
                                            id="inventory-comments"
                                            rows={5}
                                            defaultValue={inventory?.comments}
                                            placeholder="Comments"
                                            readOnly={!edit_inventory}
                                        />
                                    </div>
                                </div>
                            </div>
                        </TabsContent>
                    </Tabs>
                </CardContent>
                <FooterWrapper>
                    <Button variant="back" onClick={() => router.back()}>
                        Cancel
                    </Button>
                    {bp['tablet-md'] || activeTab === 'tasks' ? (
                        <Button
                            variant="primaryOutline"
                            onClick={() => {
                                if (!edit_task) {
                                    toast({
                                        description:
                                            'You do not have permission to edit this section',
                                        variant: 'destructive',
                                    })
                                    return
                                }
                                router.push(
                                    '/maintenance/new?inventoryId=' +
                                        inventoryID +
                                        '&vesselId=' +
                                        inventory?.vesselID +
                                        '&redirectTo=inventory',
                                )
                            }}>
                            {getResponsiveLabel(
                                bp.phablet,
                                'Create',
                                'Create task/maintenance',
                            )}
                        </Button>
                    ) : null}
                    {activeTab !== 'tasks' ? (
                        <>
                            <Button
                                variant="destructive"
                                onClick={confirmInventoryCrew}>
                                {bp.phablet ? 'Delete' : <Trash />}
                            </Button>
                            <Button onClick={handleSave}>
                                {getResponsiveLabel(
                                    bp.phablet,
                                    'Update',
                                    'Update inventory',
                                )}
                            </Button>
                        </>
                    ) : null}
                </FooterWrapper>
            </Card>
            {/* Alert Dialogs - Not touching these as requested */}
            <AlertDialogNew
                openDialog={openLocationDialog}
                setOpenDialog={setOpenLocationDialog}
                handleCreate={() => handleCreateLocation({})}
                actionText="Create Location">
                <H3>Create New Location</H3>
                <div className="my-4 flex items-center">
                    <Input
                        id={`inventory-new-location`}
                        type="text"
                        placeholder="Location"
                    />
                </div>
                <div className="flex items-center">
                    <Input
                        id={`inventory-new-location-id`}
                        type="text"
                        placeholder="Location ID"
                    />
                </div>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={openSupplierDialog}
                setOpenDialog={setOpenSupplierDialog}
                handleCreate={handleCreateSupplier}
                actionText="Create supplier"
                className="lg:max-w-lg">
                <H3>Create new supplier</H3>
                <div className="mt-4">
                    <div className="mb-4">
                        <Input
                            id={`supplier-name`}
                            type="text"
                            placeholder="Supplier name"
                        />
                    </div>
                    <div className="mb-4">
                        <Input
                            id={`supplier-website`}
                            type="text"
                            placeholder="Website"
                        />
                    </div>
                    <div className="mb-4">
                        <Input
                            id={`supplier-phone`}
                            type="text"
                            placeholder="Phone"
                        />
                    </div>
                    <div className="mb-4">
                        <Input
                            id={`supplier-email`}
                            type="email"
                            placeholder="Email"
                        />
                    </div>
                    <div>
                        <Textarea
                            id={`supplier-address`}
                            rows={4}
                            className={`${''} p-2`}
                            placeholder="Supplier address"
                        />
                    </div>
                </div>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={openCategoryDialog}
                setOpenDialog={setOpenCategoryDialog}
                handleCreate={handleCreateCategory}
                actionText="Create Category">
                <H3>Create new category</H3>
                <div className="my-4 flex items-center">
                    <Input
                        id={`inventory-new-category`}
                        type="text"
                        placeholder="Category"
                    />
                </div>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={openConfirmInventoryDeleteDialog}
                setOpenDialog={setOpenConfirmInventoryDeleteDialog}
                handleCreate={handleDeleteInventories}
                actionText="Delete Inventory">
                <H3>Delete Inventory</H3>
                <div className="my-4 flex items-center">
                    Are you sure you want to delete {inventory?.item}?
                </div>
            </AlertDialogNew>
        </>
    )
}
