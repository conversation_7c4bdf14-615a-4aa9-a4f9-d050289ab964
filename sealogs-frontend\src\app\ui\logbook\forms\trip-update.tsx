'use client'

import dayjs from 'dayjs'
import React, { useEffect, useRef, useState } from 'react'
import {
    CreateTripUpdate,
    UpdateTripUpdate,
    CreateTripEvent,
    UpdateTripEvent,
    CREATE_R2FILE,
} from '@/app/lib/graphQL/mutation'
import { GetTripEvent } from '@/app/lib/graphQL/query'
import Editor from '../../editor'
import { useLazyQuery, useMutation } from '@apollo/client'
import LocationField from '../components/location'

import TripEventModel from '@/app/offline/models/tripEvent'
import TripUpdateModel from '@/app/offline/models/tripUpdate'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import TimeField from '../components/time'
import UploadCloudFlare from '../components/upload-cf'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Check } from 'lucide-react'
import { H4 } from '@/components/ui'
import CloudFlareCaptures from '../components/CloudFlareCaptures'

export default function TripUpdate({
    currentTrip = false,
    updateTripReport,
    selectedEvent = false,
    tripReport,
    closeModal,
    locked,
    offline = false,
}: {
    currentTrip: any
    updateTripReport: any
    selectedEvent: any
    tripReport: any
    closeModal: any
    locked: any
    offline?: boolean
}) {
    const [time, setTime] = useState<any>()
    const [content, setContent] = useState<any>('')
    const [tripUpdate, setTripUpdate] = useState<any>(false)
    const [currentEvent, setCurrentEvent] = useState<any>(selectedEvent)
    const [tripEvent, setTripEvent] = useState<any>(false)
    const [attachments, setAttachments] = useState<any>([])
    const [currentLocation, setCurrentLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })

    const tripEventModel = new TripEventModel()
    const tripUpdateModel = new TripUpdateModel()
    const currentEventRef = useRef<any>(null)
    const handleTimeChange = (date: any) => {
        setTime(dayjs(date))
    }

    useEffect(() => {
        setTripUpdate(false)
        if (selectedEvent) {
            currentEventRef.current = selectedEvent
            setCurrentEvent(selectedEvent)
            getCurrentEvent(selectedEvent?.id)
        }
    }, [selectedEvent])

    useEffect(() => {
        setTripUpdate(false)
        if (currentEvent) {
            getCurrentEvent(currentEvent?.id)
        }
    }, [currentEvent])

    useEffect(() => {
        if (attachments.length > 0) {
            attachments.map((receipt: any) => {
                if (!receipt.id && tripEvent?.tripUpdate?.id > 0) {
                    createAttachments({
                        variables: {
                            input: {
                                title: receipt.title,
                                tripUpdateID: tripEvent.tripUpdate.id,
                            },
                        },
                    })
                }
            })
        }
    }, [attachments])

    const [createAttachments] = useMutation(CREATE_R2FILE, {
        onCompleted: (response) => {
            const data = response.createR2File
            const newReceipts = attachments.map((receipt: any) => {
                if (receipt.title === data.title) {
                    return {
                        ...receipt,
                        id: data.id,
                    }
                }
                return receipt
            })
            setAttachments(newReceipts)
        },
        onError: (error) => {
            console.error('Error creating fuel receipts', error)
        },
    })

    const getCurrentEvent = async (id: any) => {
        if (offline) {
            // getTripEvent
            const event = await tripEventModel.getById(id)
            if (event) {
                setTripEvent(event)
                setTripUpdate({
                    geoLocationID: event.tripUpdate?.geoLocationID,
                    title: event.tripUpdate?.title,
                    date: event.tripUpdate?.date,
                    notes: event.tripUpdate?.notes,
                    lat: event.tripUpdate?.lat,
                    long: event.tripUpdate?.long,
                })
                if (event.tripUpdate?.lat && event.tripUpdate?.long) {
                    setCurrentLocation({
                        latitude: event.tripUpdate?.lat,
                        longitude: event.tripUpdate?.long,
                    })
                }
                setContent(event.tripUpdate?.notes)
                setTime(dayjs(event.tripUpdate?.date))
            }
        } else {
            getTripEvent({
                variables: {
                    id: id,
                },
            })
        }
    }

    const [getTripEvent] = useLazyQuery(GetTripEvent, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const event = response.readOneTripEvent
            if (event) {
                setTripEvent(event)
                setTripUpdate({
                    title: event.tripUpdate?.title,
                    geoLocationID: event.tripUpdate?.geoLocationID,
                    date: event.tripUpdate?.date,
                    notes: event.tripUpdate?.notes,
                    lat: event.tripUpdate?.lat,
                    long: event.tripUpdate?.long,
                })
                if (event.tripUpdate?.lat && event.tripUpdate?.long) {
                    setCurrentLocation({
                        latitude: event.tripUpdate?.lat,
                        longitude: event.tripUpdate?.long,
                    })
                }
                setContent(event.tripUpdate?.notes)
                setTime(dayjs(event.tripUpdate?.date))
                setAttachments(event.tripUpdate.attachment.nodes)
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })

    const handleEditorChange = (newContent: any) => {
        setContent(newContent)
    }

    const handleSave = async () => {
        const variables = {
            input: {
                title: tripUpdate?.title,
                geoLocationID: tripUpdate?.geoLocationID,
                notes: content,
                lat: currentLocation.latitude.toString(),
                date: dayjs(time).format('YYYY-MM-DDTHH:mm:ss'),
                long: currentLocation.longitude.toString(),
            },
        }

        if (currentEvent) {
            if (offline) {
                // updateTripEvent
                await tripEventModel.save({
                    id: +currentEvent.id,
                    eventCategory: 'TripUpdate',
                    logBookEntrySectionID: currentTrip.id,
                })
                getCurrentEvent(currentEvent?.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
            } else {
                updateTripEvent({
                    variables: {
                        input: {
                            id: +currentEvent.id,
                            eventCategory: 'TripUpdate',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
            if (offline) {
                // updateTripUpdate
                await tripUpdateModel.save({
                    id: +selectedEvent?.tripUpdate?.id,
                    ...variables.input,
                })
            } else {
                updateTripUpdate({
                    variables: {
                        input: {
                            id: +selectedEvent?.tripUpdate?.id,
                            ...variables.input,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                // createTripEvent
                const data = await tripEventModel.save({
                    id: generateUniqueId(),
                    eventCategory: 'TripUpdate',
                    logBookEntrySectionID: currentTrip.id,
                })
                currentEventRef.current = data
                setCurrentEvent(data)
                // createTripUpdate
                const tripUpdateData = await tripUpdateModel.save({
                    id: generateUniqueId(),
                    geoLocationID: tripUpdate?.geoLocationID,
                    notes: content,
                    lat: currentLocation.latitude.toString(),
                    long: currentLocation.longitude.toString(),
                    date: dayjs(time).format('YYYY-MM-DDTHH:mm:ssZ'),
                })
                setTimeout(async () => {
                    // updateTripEvent
                    await tripEventModel.save({
                        id: currentEventRef.current?.id,
                        tripUpdateID: tripUpdateData.id,
                    })
                    getCurrentEvent(currentEventRef.current?.id)
                    updateTripReport({
                        id: [
                            ...tripReport.map((trip: any) => trip.id),
                            currentTrip.id,
                        ],
                    })
                }, 200)
                closeModal()
                // updateTripEvent
                await tripEventModel.save({
                    id: data.id,
                    eventCategory: 'TripUpdate',
                    tripUpdateID: currentTrip.id,
                })
                getCurrentEvent(currentEventRef.current?.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
            } else {
                createTripEvent({
                    variables: {
                        input: {
                            eventCategory: 'TripUpdate',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
        }
    }

    const [createTripEvent] = useMutation(CreateTripEvent, {
        onCompleted: (response) => {
            const data = response.createTripEvent
            currentEventRef.current = data
            setCurrentEvent(data)
            createTripUpdate({
                variables: {
                    input: {
                        title: tripUpdate?.title,
                        geoLocationID: tripUpdate?.geoLocationID,
                        notes: content,
                        lat: currentLocation.latitude.toString(),
                        long: currentLocation.longitude.toString(),
                        date: dayjs(time).format('YYYY-MM-DDTHH:mm:ss'),
                    },
                },
            })
            updateTripEvent({
                variables: {
                    input: {
                        id: data.id,
                        eventCategory: 'TripUpdate',
                    },
                },
            })
        },
        onError: (error) => {
            console.error('Error creating trip event', error)
        },
    })

    const [createTripUpdate] = useMutation(CreateTripUpdate, {
        onCompleted: (response) => {
            const data = response.createTripUpdate
            setTimeout(() => {
                updateTripEvent({
                    variables: {
                        input: {
                            id: currentEventRef.current?.id,
                            tripUpdateID: data.id,
                        },
                    },
                })
            }, 200)
            if (attachments.length > 0) {
                attachments.map((receipt: any) => {
                    if (!receipt.id && data.id) {
                        createAttachments({
                            variables: {
                                input: {
                                    title: receipt.title,
                                    tripUpdateID: data.id,
                                },
                            },
                        })
                    }
                })
            }
            closeModal()
        },
        onError: (error) => {
            console.error('Error creating refuelling', error)
        },
    })

    const [updateTripUpdate] = useMutation(UpdateTripUpdate, {
        onCompleted: () => {
            // Successfully updated trip update
        },
        onError: (error) => {
            console.error('Error updating refuelling', error)
        },
    })

    const [updateTripEvent] = useMutation(UpdateTripEvent, {
        onCompleted: () => {
            getCurrentEvent(currentEventRef.current?.id)
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), currentTrip.id],
            })
        },
        onError: (error) => {
            console.error('Error updating trip event', error)
        },
    })

    const handleLocationChange = (value: any) => {
        // If value is null or undefined, return early
        if (!value) return

        // Check if the value is from dropdown selection (has 'value' property)
        if (value.value) {
            // Handle location selected from dropdown
            setTripUpdate({
                ...tripUpdate,
                geoLocationID: +value.value,
                lat: null,
                long: null,
            })
        } else if (
            value.latitude !== undefined &&
            value.longitude !== undefined
        ) {
            // Handle direct coordinates input
            setTripUpdate({
                ...tripUpdate,
                geoLocationID: 0, // Reset geoLocationID when using direct coordinates
                lat: value.latitude,
                long: value.longitude,
            })
        }
    }

    return (
        <div className="space-y-6">
            <Label htmlFor="title" label="Title">
                <Input
                    id="title"
                    type="text"
                    className="w-full"
                    placeholder="Title"
                    value={tripUpdate?.title}
                    onChange={(e) =>
                        setTripUpdate({
                            ...tripUpdate,
                            title: e.target.value,
                        })
                    }
                    disabled={locked}
                />
            </Label>

            <Label htmlFor="location" label="Location of update">
                <LocationField
                    offline={offline}
                    setCurrentLocation={setCurrentLocation}
                    handleLocationChange={handleLocationChange}
                    currentEvent={tripEvent.tripUpdate}
                />
            </Label>

            <Label htmlFor="time" label="Time for reporting">
                <TimeField
                    time={dayjs(time).format('HH:mm')}
                    handleTimeChange={handleTimeChange}
                    timeID="fuel-added-time"
                    fieldName="Time"
                    hideButton={locked}
                />
            </Label>

            {(!currentEvent || tripUpdate) && (
                <Label label="Comments" htmlFor="comments">
                    <Editor
                        id="comments"
                        placeholder="Add comments and any update notes..."
                        content={content}
                        handleEditorChange={handleEditorChange}
                        disabled={locked}
                    />
                </Label>
            )}

            <Label label="Attachments">
                {/* <UploadCloudFlare
                    files={attachments}
                    setFiles={setAttachments}
                /> */}
            </Label>
            <div className="w-full flex flex-col space-y-2">
                <CloudFlareCaptures
                    inputId={selectedEvent?.id || 0}
                    sectionId={currentTrip.id}
                    buttonType={'button'}
                    sectionName={'tripEventID'}
                />
            </div>

            <div className="flex justify-end gap-2">
                <Button
                    variant="back"
                    iconLeft={ArrowLeft}
                    onClick={closeModal}>
                    Cancel
                </Button>
                <Button iconLeft={Check} disabled={locked} onClick={handleSave}>
                    {selectedEvent ? 'Update' : 'Save'}
                </Button>
            </div>
        </div>
    )
}
