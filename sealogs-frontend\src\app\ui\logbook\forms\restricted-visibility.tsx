'use client'

import dayjs from 'dayjs'
import React, { useEffect, useRef, useState } from 'react'
import {
    CreateEventType_RestrictedVisibility,
    UpdateEventType_RestrictedVisibility,
    CreateTripEvent,
    UpdateTripEvent,
    CreateRiskFactor,
    UpdateRiskFactor,
    CreateMitigationStrategy,
} from '@/app/lib/graphQL/mutation'
import { GetRiskFactors, GetTripEvent } from '@/app/lib/graphQL/query'
import { useLazyQuery, useMutation } from '@apollo/client'
import { ArrowLeft, Check } from 'lucide-react'

import LocationField from '../components/location'
import TimeField from '../components/time'
import TripEventModel from '@/app/offline/models/tripEvent'
import EventType_RestrictedVisibilityModel from '@/app/offline/models/eventType_RestrictedVisibility'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import {
    Sheet,
    She<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>onte<PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@/components/ui/sheet'
import Editor from '../../editor'
import { Combobox } from '@/components/ui/comboBox'
import { useSearchParams } from 'next/navigation'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Slider } from '@/components/ui/slider'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { Separator } from '@/components/ui/separator'
import { H4 } from '@/components/ui/typography'
import { RadioGroup } from '@/components/ui/radio-group'
import { Button } from '@/components/ui/button'
import { CheckFieldLabel } from '@/components/ui/check-field-label'
import CloudFlareCaptures from '../components/CloudFlareCaptures'

export default function RestrictedVisibility({
    currentTrip = false,
    updateTripReport,
    selectedEvent = false,
    tripReport,
    closeModal,
    logBookConfig,
    locked,
    offline = false,
    members,
}: {
    currentTrip: any
    updateTripReport: any
    selectedEvent: any
    tripReport: any
    closeModal: any
    logBookConfig: any
    locked: any
    offline?: boolean
    members: any
}) {
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const [crossingTime, setCrossingTime] = useState<any>()
    const [crossedTime, setCrossedTime] = useState<any>()
    const [restrictedVisibility, setRestrictedVisibility] = useState<any>(false)
    const [tripEvent, setTripEvent] = useState<any>(false)
    const [currentEvent, setCurrentEvent] = useState<any>(selectedEvent)
    const [openProcedureChecklist, setOpenProcedureChecklist] = useState(false)
    // Used in the component to track if SOP should be displayed
    const [displaySOP, setDisplaySOP] = useState(false)
    const [selectedAuthor, setSelectedAuthor] = useState<any>()
    const [openRiskDialog, setOpenRiskDialog] = useState(false)
    const [currentRisk, setCurrentRisk] = useState<any>()
    const [riskValue, setRiskValue] = useState<any>(null)
    const [riskToDelete, setRiskToDelete] = useState<any>()
    const [riskFactors, setRiskFactors] = useState<any>([])
    const [crewMembers, setCrewMembers] = useState<any>([])
    const [allRisks, setAllRisks] = useState<any>(false)
    const [content, setContent] = useState<any>()
    const [openRecommendedstrategy, setOpenRecommendedstrategy] =
        useState(false)
    const [allRiskFactors, setAllRiskFactors] = useState<any>([])
    const [currentStrategies, setCurrentStrategies] = useState<any>([])
    const [recommendedStratagies, setRecommendedStratagies] =
        useState<any>(false)
    const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false)
    const [currentStartLocation, setCurrentStartLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })
    const [currentEndLocation, setCurrentEndLocation] = useState<any>({
        latitude: '',
        longitude: '',
    })
    const tripEventModel = new TripEventModel()
    const restrictedVisibilityModel = new EventType_RestrictedVisibilityModel()
    const currentEventRef = useRef<any>(null)
    const [closeOnSave, setCloseOnSave] = useState(false)
    const handleCrossingTimeChange = (date: any) => {
        setCrossingTime(dayjs(date).format('HH:mm'))
    }

    const handleCrossedTimeChange = (date: any) => {
        setCrossedTime(dayjs(date).format('HH:mm'))
    }

    // Function to set display SOP state - used in the component
    const handleSetDisplaySOP = (value: boolean) => {
        setDisplaySOP(value)
        setOpenProcedureChecklist(value)
    }

    const handleEditorChange = (newContent: any) => {
        setContent(newContent)
    }

    const handleDeleteRisk = async () => {
        updateRiskFactor({
            variables: {
                input: {
                    id: riskToDelete.id,
                    eventType_RestrictedVisibilityID: 0,
                    vesselID: 0,
                },
            },
        })
        setOpenDeleteConfirmation(false)
    }

    const handleSetRiskToDelete = (risk: any) => {
        setRiskToDelete(risk)
        setOpenDeleteConfirmation(true)
    }

    const handleNewStrategy = async () => {
        if (content) {
            createMitigationStrategy({
                variables: {
                    input: {
                        strategy: content,
                    },
                },
            })
        }
        setOpenRecommendedstrategy(false)
    }

    const [createMitigationStrategy] = useMutation(CreateMitigationStrategy, {
        onCompleted: (data) => {
            setCurrentStrategies([
                ...currentStrategies,
                { id: data.createMitigationStrategy.id, strategy: content },
            ])
            setContent('')
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const handleSetCurrentStrategies = (strategy: any) => {
        if (currentStrategies.length > 0) {
            if (currentStrategies.find((s: any) => s.id === strategy.id)) {
                setCurrentStrategies(
                    currentStrategies.filter((s: any) => s.id !== strategy.id),
                )
            } else {
                setCurrentStrategies([...currentStrategies, strategy])
            }
        } else {
            setCurrentStrategies([strategy])
        }
    }

    const handleSetRiskValue = (v: any) => {
        setRiskValue({
            value: v.title,
            label: v.title,
        })
        if (v.mitigationStrategy.nodes) {
            setCurrentStrategies(v.mitigationStrategy.nodes)
        }
        if (
            allRiskFactors?.filter(
                (risk: any) =>
                    risk.title === v.title &&
                    risk.mitigationStrategy.nodes?.length > 0,
            ).length > 0
        ) {
            setRecommendedStratagies(
                Array.from(
                    new Set(
                        allRiskFactors
                            ?.filter(
                                (r: any) =>
                                    r.title === v.title &&
                                    r.mitigationStrategy.nodes?.length > 0,
                            )
                            .map((r: any) => r.mitigationStrategy.nodes)[0]
                            .map((s: any) => ({
                                id: s.id,
                                strategy: s.strategy,
                            })),
                    ),
                ),
            )
        } else {
            setRecommendedStratagies(false)
        }
    }

    // This function is no longer used as its functionality is now in handleRiskValue

    useEffect(() => {
        getRiskFactors({
            variables: {
                filter: { type: { eq: 'RestrictedVisibility' } },
            },
        })
    }, [])

    useEffect(() => {
        getRiskFactors({
            variables: {
                filter: { type: { eq: 'RestrictedVisibility' } },
            },
        })
    }, [openProcedureChecklist])

    const [getRiskFactors] = useLazyQuery(GetRiskFactors, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (data) => {
            const risks = Array.from(
                new Set(
                    data.readRiskFactors.nodes?.map((risk: any) => risk.title),
                ),
            )?.map((risk: any) => ({ label: risk, value: risk }))
            setAllRisks(risks)
            setAllRiskFactors(data.readRiskFactors.nodes)
            setRiskFactors(
                data.readRiskFactors.nodes.filter(
                    (r: any) =>
                        r.eventType_RestrictedVisibilityID ==
                        restrictedVisibility?.id,
                ),
            )
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    useEffect(() => {
        setRestrictedVisibility(false)
        if (selectedEvent) {
            setCurrentEvent(selectedEvent)
            getCurrentEvent(selectedEvent?.id)
        }
    }, [selectedEvent])

    useEffect(() => {
        setRestrictedVisibility(false)
        if (currentEvent) {
            getCurrentEvent(currentEvent?.id)
        }
    }, [currentEvent])

    const getCurrentEvent = async (id: any) => {
        getTripEvent({
            variables: {
                id: id,
            },
        })
    }

    const [getTripEvent] = useLazyQuery(GetTripEvent, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const event = response.readOneTripEvent
            if (event) {
                setTripEvent(event)
                setRestrictedVisibility({
                    id: +event.eventType_RestrictedVisibility.id,
                    startLocationID:
                        event.eventType_RestrictedVisibility?.startLocationID,
                    crossingTime:
                        event.eventType_RestrictedVisibility?.crossingTime,
                    estSafeSpeed:
                        event.eventType_RestrictedVisibility?.estSafeSpeed,
                    stopAssessPlan:
                        event.eventType_RestrictedVisibility?.stopAssessPlan,
                    crewBriefing:
                        event.eventType_RestrictedVisibility?.crewBriefing,
                    navLights: event.eventType_RestrictedVisibility?.navLights,
                    soundSignal:
                        event.eventType_RestrictedVisibility?.soundSignal,
                    lookout: event.eventType_RestrictedVisibility?.lookout,
                    soundSignals:
                        event.eventType_RestrictedVisibility?.soundSignals,
                    radarWatch:
                        event.eventType_RestrictedVisibility?.radarWatch,
                    radioWatch:
                        event.eventType_RestrictedVisibility?.radioWatch,
                    endLocationID:
                        event.eventType_RestrictedVisibility?.endLocationID,
                    crossedTime:
                        event.eventType_RestrictedVisibility?.crossedTime,
                    approxSafeSpeed:
                        event.eventType_RestrictedVisibility?.approxSafeSpeed,
                    report: event.eventType_RestrictedVisibility?.report,
                    startLat: event.eventType_RestrictedVisibility?.startLat,
                    startLong: event.eventType_RestrictedVisibility?.startLong,
                    endLat: event.eventType_RestrictedVisibility?.endLat,
                    endLong: event.eventType_RestrictedVisibility?.endLong,
                })
                if (
                    event.eventType_RestrictedVisibility?.startLat &&
                    event.eventType_RestrictedVisibility?.startLong
                ) {
                    setCurrentStartLocation({
                        latitude:
                            event.eventType_RestrictedVisibility?.startLat,
                        longitude:
                            event.eventType_RestrictedVisibility?.startLong,
                    })
                }
                if (
                    event.eventType_RestrictedVisibility?.endLat &&
                    event.eventType_RestrictedVisibility?.endLong
                ) {
                    setCurrentEndLocation({
                        latitude: event.eventType_RestrictedVisibility?.endLat,
                        longitude:
                            event.eventType_RestrictedVisibility?.endLong,
                    })
                }
                setCrossedTime(
                    event.eventType_RestrictedVisibility?.crossedTime,
                )
                setCrossingTime(
                    event.eventType_RestrictedVisibility?.crossingTime,
                )
                if (event.eventType_RestrictedVisibility?.memberID > 0) {
                    setSelectedAuthor({
                        label: `${event.eventType_RestrictedVisibility?.member.firstName} ${event.eventType_RestrictedVisibility?.member.surname}`,
                        value: event.eventType_RestrictedVisibility?.memberID,
                    })
                }
                if (
                    event.eventType_RestrictedVisibility?.stopAssessPlan ||
                    event.eventType_RestrictedVisibility?.crewBriefing ||
                    event.eventType_RestrictedVisibility?.navLights ||
                    event.eventType_RestrictedVisibility?.soundSignal ||
                    event.eventType_RestrictedVisibility?.lookout ||
                    event.eventType_RestrictedVisibility?.soundSignals ||
                    event.eventType_RestrictedVisibility?.radarWatch ||
                    event.eventType_RestrictedVisibility?.radioWatch ||
                    event.eventType_RestrictedVisibility?.memberID > 0
                ) {
                    setDisplaySOP(true)
                }
            }
        },
        onError: (error) => {
            console.error('Error getting current event', error)
        },
    })

    const handleSave = async () => {
        const variables = {
            input: {
                startLocationID: restrictedVisibility?.startLocationID,
                crossingTime: crossingTime ?? dayjs().format('HH:mm'),
                estSafeSpeed: restrictedVisibility?.estSafeSpeed,
                stopAssessPlan: restrictedVisibility?.stopAssessPlan,
                crewBriefing: restrictedVisibility?.crewBriefing,
                navLights: restrictedVisibility?.navLights,
                soundSignal: restrictedVisibility?.soundSignal,
                lookout: restrictedVisibility?.lookout,
                soundSignals: restrictedVisibility?.soundSignals,
                radarWatch: restrictedVisibility?.radarWatch,
                radioWatch: restrictedVisibility?.radioWatch,
                endLocationID: restrictedVisibility?.endLocationID,
                crossedTime: crossedTime ?? dayjs().format('HH:mm'),
                approxSafeSpeed: restrictedVisibility?.approxSafeSpeed,
                report: restrictedVisibility?.report,
                startLat: currentStartLocation.latitude.toString(),
                startLong: currentStartLocation.longitude.toString(),
                endLat: currentEndLocation.latitude.toString(),
                endLong: currentEndLocation.longitude.toString(),
                memberID: selectedAuthor?.value,
            },
        }
        if (currentEvent) {
            if (offline) {
                await tripEventModel.save({
                    id: +currentEvent.id,
                    eventCategory: 'RestrictedVisibility',
                    logBookEntrySectionID: currentTrip.id,
                })
                getCurrentEvent(currentEvent?.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
            } else {
                updateTripEvent({
                    variables: {
                        input: {
                            id: +currentEvent.id,
                            eventCategory: 'RestrictedVisibility',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }

            if (offline) {
                await restrictedVisibilityModel.save({
                    id: +selectedEvent?.eventType_RestrictedVisibilityID,
                    ...variables.input,
                })
            } else {
                updateEventType_RestrictedVisibility({
                    variables: {
                        input: {
                            id: +selectedEvent?.eventType_RestrictedVisibilityID,
                            ...variables.input,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                const tripEventData = await tripEventModel.save({
                    id: generateUniqueId(),
                    eventCategory: 'RestrictedVisibility',
                    logBookEntrySectionID: currentTrip.id,
                })
                setCurrentEvent(tripEventData)
                const restrictedVisibilityData =
                    await restrictedVisibilityModel.save({
                        id: generateUniqueId(),
                        startLocationID: restrictedVisibility?.startLocationID,
                        crossingTime: crossingTime,
                        estSafeSpeed: restrictedVisibility?.estSafeSpeed,
                        stopAssessPlan: restrictedVisibility?.stopAssessPlan,
                        crewBriefing: restrictedVisibility?.crewBriefing,
                        navLights: restrictedVisibility?.navLights,
                        soundSignal: restrictedVisibility?.soundSignal,
                        lookout: restrictedVisibility?.lookout,
                        soundSignals: restrictedVisibility?.soundSignals,
                        radarWatch: restrictedVisibility?.radarWatch,
                        radioWatch: restrictedVisibility?.radioWatch,
                        endLocationID: restrictedVisibility?.endLocationID,
                        crossedTime: crossedTime,
                        approxSafeSpeed: restrictedVisibility?.approxSafeSpeed,
                        report: restrictedVisibility?.report,
                        startLat: currentStartLocation.latitude.toString(),
                        startLong: currentStartLocation.longitude.toString(),
                        endLat: currentEndLocation.latitude.toString(),
                        endLong: currentEndLocation.longitude.toString(),
                    })
                await tripEventModel.save({
                    id: tripEventData.id,
                    eventCategory: 'RestrictedVisibility',
                    eventType_RestrictedVisibilityID:
                        restrictedVisibilityData.id,
                })

                getCurrentEvent(tripEventData.id)
                updateTripReport({
                    id: [
                        ...tripReport.map((trip: any) => trip.id),
                        currentTrip.id,
                    ],
                })
                if (closeOnSave) {
                    setCloseOnSave(false)
                    closeModal()
                }
            } else {
                createTripEvent({
                    variables: {
                        input: {
                            eventCategory: 'RestrictedVisibility',
                            logBookEntrySectionID: currentTrip.id,
                        },
                    },
                })
            }
        }
    }

    const [createTripEvent] = useMutation(CreateTripEvent, {
        onCompleted: (response) => {
            const data = response.createTripEvent
            currentEventRef.current = data
            setCurrentEvent(data)
            createEventType_RestrictedVisibility({
                variables: {
                    input: {
                        startLocationID: restrictedVisibility?.startLocationID,
                        crossingTime: crossingTime,
                        estSafeSpeed: restrictedVisibility?.estSafeSpeed,
                        stopAssessPlan: restrictedVisibility?.stopAssessPlan,
                        crewBriefing: restrictedVisibility?.crewBriefing,
                        navLights: restrictedVisibility?.navLights,
                        soundSignal: restrictedVisibility?.soundSignal,
                        lookout: restrictedVisibility?.lookout,
                        soundSignals: restrictedVisibility?.soundSignals,
                        radarWatch: restrictedVisibility?.radarWatch,
                        radioWatch: restrictedVisibility?.radioWatch,
                        endLocationID: restrictedVisibility?.endLocationID,
                        crossedTime: crossedTime,
                        approxSafeSpeed: restrictedVisibility?.approxSafeSpeed,
                        report: restrictedVisibility?.report,
                        startLat: currentStartLocation.latitude.toString(),
                        startLong: currentStartLocation.longitude.toString(),
                        endLat: currentEndLocation.latitude.toString(),
                        endLong: currentEndLocation.longitude.toString(),
                        memberID: selectedAuthor?.value,
                    },
                },
            })
            updateTripEvent({
                variables: {
                    input: {
                        id: data.id,
                        eventCategory: 'RestrictedVisibility',
                        eventType_RestrictedVisibilityID: data.id,
                    },
                },
            })
        },
        onError: (error) => {
            console.error('Error creating trip event', error)
        },
    })

    const [createEventType_RestrictedVisibility] = useMutation(
        CreateEventType_RestrictedVisibility,
        {
            onCompleted: (response) => {
                const data = response.createEventType_RestrictedVisibility
                updateTripEvent({
                    variables: {
                        input: {
                            id: currentEventRef.current?.id,
                            eventType_RestrictedVisibilityID: data.id,
                        },
                    },
                })
                if (closeOnSave) {
                    setCloseOnSave(false)
                    closeModal()
                }
            },
            onError: (error) => {
                console.error('Error creating Person rescue', error)
            },
        },
    )

    const [updateEventType_RestrictedVisibility] = useMutation(
        UpdateEventType_RestrictedVisibility,
        {
            onCompleted: () => {
                // Successfully updated restricted visibility
                if (closeOnSave) {
                    setCloseOnSave(false)
                    closeModal()
                }
            },
            onError: (error) => {
                console.error('Error updating restricted visibility', error)
            },
        },
    )

    const [updateTripEvent] = useMutation(UpdateTripEvent, {
        onCompleted: () => {
            getCurrentEvent(currentEvent?.id)
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), currentTrip.id],
            })
        },
        onError: (error) => {
            console.error('Error updating trip event', error)
        },
    })

    const displayField = (fieldName: string) => {
        const eventTypesConfig =
            logBookConfig?.customisedLogBookComponents?.nodes?.filter(
                (node: any) =>
                    node.componentClass === 'EventType_LogBookComponent',
            )
        if (
            eventTypesConfig?.length > 0 &&
            eventTypesConfig[0]?.customisedComponentFields?.nodes.filter(
                (field: any) =>
                    field.fieldName === fieldName && field.status !== 'Off',
            ).length > 0
        ) {
            return true
        }
        return false
    }

    const handleStartLocationChange = (value: any) => {
        // If value is null or undefined, return early
        if (!value) return

        // Check if the value is from dropdown selection (has 'value' property)
        if (value.value) {
            // Handle location selected from dropdown
            setRestrictedVisibility({
                ...restrictedVisibility,
                startLocationID: +value.value,
                startLat: null,
                startLong: null,
            })
        } else if (
            value.latitude !== undefined &&
            value.longitude !== undefined
        ) {
            // Handle direct coordinates input
            setRestrictedVisibility({
                ...restrictedVisibility,
                startLocationID: 0, // Reset locationID when using direct coordinates
                startLat: value.latitude,
                startLong: value.longitude,
            })
        }
    }

    const handleEndLocationChange = (value: any) => {
        // If value is null or undefined, return early
        if (!value) return

        // Check if the value is from dropdown selection (has 'value' property)
        if (value.value) {
            // Handle location selected from dropdown
            setRestrictedVisibility({
                ...restrictedVisibility,
                endLocationID: +value.value,
                endLat: null,
                endLong: null,
            })
        } else if (
            value.latitude !== undefined &&
            value.longitude !== undefined
        ) {
            // Handle direct coordinates input
            setRestrictedVisibility({
                ...restrictedVisibility,
                endLocationID: 0, // Reset locationID when using direct coordinates
                endLat: value.latitude,
                endLong: value.longitude,
            })
        }
    }
    const startLocationData = {
        geoLocationID:
            restrictedVisibility?.startLocationID > 0
                ? restrictedVisibility.startLocationID
                : tripEvent.eventType_RestrictedVisibility?.startLocationID,
        lat: tripEvent.eventType_RestrictedVisibility?.startLat,
        long: tripEvent.eventType_RestrictedVisibility?.startLong,
    }

    const endLocationData = {
        geoLocationID:
            restrictedVisibility?.endLocationID > 0
                ? restrictedVisibility.endLocationID
                : tripEvent.eventType_RestrictedVisibility?.endLocationID,
        lat: tripEvent.eventType_RestrictedVisibility?.endLat,
        long: tripEvent.eventType_RestrictedVisibility?.endLong,
    }

    useEffect(() => {
        if (members) {
            const crewMembers = members.map((member: any) => {
                return {
                    label: `${member.crewMember.firstName ?? ''} ${member.crewMember.surname ?? ''}`,
                    value: member.crewMemberID,
                }
            })
            setCrewMembers(crewMembers)
        }
    }, [members])

    const riskImpacts = [
        { value: 'Low', label: 'Low impact' },
        { value: 'Medium', label: 'Medium impact' },
        { value: 'High', label: 'High impact' },
        { value: 'Severe', label: 'Severe impact' },
    ]

    const handleSaveRisk = async () => {
        if (currentRisk.id > 0) {
            updateRiskFactor({
                variables: {
                    input: {
                        id: currentRisk.id,
                        type: 'RestrictedVisibility',
                        title: currentRisk.title,
                        impact: currentRisk?.impact
                            ? currentRisk?.impact
                            : 'Low',
                        probability: currentRisk?.probability
                            ? currentRisk?.probability
                            : 5,
                        mitigationStrategy:
                            currentStrategies.length > 0
                                ? currentStrategies
                                      .map((s: any) => s.id)
                                      .join(',')
                                : '',
                        eventType_RestrictedVisibilityID:
                            restrictedVisibility?.id,
                    },
                },
            })
        } else {
            createRiskFactor({
                variables: {
                    input: {
                        type: 'RestrictedVisibility',
                        title: currentRisk.title,
                        impact: currentRisk?.impact
                            ? currentRisk?.impact
                            : 'Low',
                        probability: currentRisk?.probability
                            ? currentRisk?.probability
                            : 5,
                        mitigationStrategy:
                            currentStrategies.length > 0
                                ? currentStrategies
                                      .map((s: any) => s.id)
                                      .join(',')
                                : '',
                        eventType_RestrictedVisibilityID:
                            restrictedVisibility?.id,
                        vesselID: vesselID,
                    },
                },
            })
        }
    }

    const [createRiskFactor] = useMutation(CreateRiskFactor, {
        onCompleted: () => {
            setOpenRiskDialog(false)
            getRiskFactors({
                variables: {
                    filter: { type: { eq: 'RestrictedVisibility' } },
                },
            })
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [updateRiskFactor] = useMutation(UpdateRiskFactor, {
        onCompleted: () => {
            setOpenRiskDialog(false)
            getRiskFactors({
                variables: {
                    filter: { type: { eq: 'RestrictedVisibility' } },
                },
            })
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const handleCreateRisk = (inputValue: any) => {
        setCurrentRisk({
            ...currentRisk,
            title: inputValue,
        })
        setRiskValue({ value: inputValue, label: inputValue })
        if (allRisks) {
            const risk = [...allRisks, { value: inputValue, label: inputValue }]
            setAllRisks(risk)
        } else {
            setAllRisks([{ value: inputValue, label: inputValue }])
        }
    }

    const handleRiskValue = (v: any) => {
        // If v is null, user cleared the selection
        if (!v) {
            setCurrentRisk({
                ...currentRisk,
                title: '',
            })
            setRiskValue(null)
            setRecommendedStratagies(false)
            return
        }

        // Check if this is a new value (not in existing options)
        const isNewValue = !allRisks.some((risk: any) => risk.value === v.value)

        if (isNewValue) {
            // Handle creating a new risk option
            handleCreateRisk(v.value)
        } else {
            // Handle selecting an existing risk
            setCurrentRisk({
                ...currentRisk,
                title: v.value,
            })
            setRiskValue({ value: v.value, label: v.value })

            if (
                allRiskFactors?.filter(
                    (risk: any) =>
                        risk.title === v.value &&
                        risk.mitigationStrategy.nodes?.length > 0,
                ).length > 0
            ) {
                setRecommendedStratagies(
                    Array.from(
                        new Set(
                            allRiskFactors
                                ?.filter(
                                    (r: any) =>
                                        r.title === v.value &&
                                        r.mitigationStrategy.nodes?.length > 0,
                                )
                                .map((r: any) => r.mitigationStrategy.nodes)[0]
                                .map((s: any) => ({
                                    id: s.id,
                                    strategy: s.strategy,
                                })),
                        ),
                    ),
                )
            } else {
                setRecommendedStratagies(false)
            }
        }
    }

    return (
        <div className="space-y-8">
            {displayField('RestrictedVisibility_CrossingTime') ||
            displayField('RestrictedVisibility_StartLocation') ||
            displayField('RestrictedVisibility_EstSafeSpeed') ? (
                <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {displayField('RestrictedVisibility_StartLocation') && (
                            <Label
                                htmlFor="startLocation"
                                disabled={locked}
                                label="Location where limited visibility starts">
                                <LocationField
                                    offline={offline}
                                    setCurrentLocation={setCurrentStartLocation}
                                    handleLocationChange={
                                        handleStartLocationChange
                                    }
                                    currentEvent={startLocationData}
                                />
                            </Label>
                        )}
                        {displayField('RestrictedVisibility_CrossingTime') && (
                            <Label
                                htmlFor="crossingTime"
                                disabled={locked}
                                label="Time where limited visibility starts">
                                <TimeField
                                    time={crossingTime}
                                    handleTimeChange={handleCrossingTimeChange}
                                    timeID="crossingTime"
                                    fieldName="Time vis. restriction started"
                                />
                            </Label>
                        )}
                        {displayField('RestrictedVisibility_EstSafeSpeed') && (
                            <Label
                                htmlFor="estSafeSpeed"
                                disabled={locked}
                                label="Estimated safe speed for conditions">
                                <Input
                                    id="estSafeSpeed"
                                    type="number"
                                    placeholder="Enter safe speed for conditions"
                                    min={1}
                                    className="w-full"
                                    value={
                                        restrictedVisibility?.estSafeSpeed ??
                                        undefined
                                    }
                                    onChange={(e: any) => {
                                        setRestrictedVisibility({
                                            ...restrictedVisibility,
                                            estSafeSpeed: e.target.value,
                                        })
                                    }}
                                />
                            </Label>
                        )}
                    </div>
                </>
            ) : null}
            {displayField('RestrictedVisibility_StopAssessPlan') ||
            displayField('RestrictedVisibility_CrewBriefing') ||
            displayField('RestrictedVisibility_NavLights') ||
            displayField('RestrictedVisibility_SoundSignal') ||
            displayField('RestrictedVisibility_Lookout') ||
            displayField('RestrictedVisibility_SoundSignals') ||
            displayField('RestrictedVisibility_RadarWatch') ||
            displayField('RestrictedVisibility_RadioWatch') ? (
                <>
                    <Separator />
                    <div className="flex justify-between items-center">
                        <div className="flex items-center gap-2">
                            <CheckFieldLabel
                                id="displaySOP"
                                checked={restrictedVisibility !== false}
                                onClick={() => setOpenProcedureChecklist(true)}
                                label="Safe operating procedures checklist"
                                variant="success"
                            />
                        </div>
                        {/* {displaySOP && (
                            <Button
                                variant="primary"
                                onClick={() => setOpenProcedureChecklist(true)}>
                                Safe operating procedures checklist
                            </Button>
                        )} */}
                    </div>
                    <Separator />
                </>
            ) : null}
            {displayField('RestrictedVisibility_EndLocation') ||
            displayField('RestrictedVisibility_CrossedTime') ||
            displayField('RestrictedVisibility_ApproxSafeSpeed') ||
            displayField('RestrictedVisibility_Report') ? (
                <div className="flex flex-col space-y-6">
                    {displayField('RestrictedVisibility_EndLocation') && (
                        <Label
                            htmlFor="endLocation"
                            disabled={locked}
                            label="Location where limited visibility ends">
                            <LocationField
                                offline={offline}
                                setCurrentLocation={setCurrentEndLocation}
                                handleLocationChange={handleEndLocationChange}
                                currentEvent={endLocationData}
                            />
                        </Label>
                    )}
                    {displayField('RestrictedVisibility_CrossedTime') && (
                        <Label
                            htmlFor="crossedTime"
                            disabled={locked}
                            label="Time when limited visibility ends">
                            <TimeField
                                time={crossedTime}
                                handleTimeChange={handleCrossedTimeChange}
                                timeID="crossedTime"
                                fieldName="End time"
                            />
                        </Label>
                    )}
                    {displayField('RestrictedVisibility_ApproxSafeSpeed') && (
                        <Label
                            htmlFor="approxSafeSpeed"
                            disabled={locked}
                            label="Approximate average speed during restricted visibility period">
                            <Input
                                id="approxSafeSpeed"
                                type="number"
                                placeholder="Enter approximate average speed"
                                min={1}
                                className="w-full"
                                value={
                                    restrictedVisibility?.approxSafeSpeed ??
                                    undefined
                                }
                                onChange={(e: any) => {
                                    setRestrictedVisibility({
                                        ...restrictedVisibility,
                                        approxSafeSpeed: e.target.value,
                                    })
                                }}
                            />
                        </Label>
                    )}
                    {displayField('RestrictedVisibility_Report') && (
                        <Label
                            htmlFor="restricted-visibility-report"
                            disabled={locked}
                            label="Comments or observations">
                            <Textarea
                                id="restricted-visibility-report"
                                className="w-full min-h-[120px]"
                                rows={4}
                                placeholder="Add any comments or observations pertinant to the limited visibility event"
                                value={
                                    restrictedVisibility?.report ?? undefined
                                }
                                onChange={(e: any) => {
                                    setRestrictedVisibility({
                                        ...restrictedVisibility,
                                        report: e.target.value,
                                    })
                                }}
                            />
                        </Label>
                    )}
                    <div className="col-span-1 md:col-span-2 flex flex-col sm:flex-row justify-end gap-2 mt-2">
                        <Button
                            variant="back"
                            iconLeft={ArrowLeft}
                            onClick={closeModal}>
                            Cancel
                        </Button>
                        <Button
                            variant="primary"
                            onClick={
                                locked
                                    ? () => {}
                                    : () => {
                                          setCloseOnSave(true)
                                          handleSave()
                                      }
                            }
                            disabled={locked}
                            iconLeft={Check}>
                            {selectedEvent ? 'Update' : 'Save'}
                        </Button>
                    </div>
                </div>
            ) : null}
            <Sheet
                open={openProcedureChecklist}
                onOpenChange={setOpenProcedureChecklist}>
                <SheetContent side="right">
                    <SheetHeader>
                        <SheetTitle>
                            Safe operating procedures checklist
                        </SheetTitle>
                    </SheetHeader>
                    <SheetBody>
                        <div
                            className={`${locked ? 'pointer-events-none' : ''} grid grid-cols-1 gap-6`}>
                            <div className="space-y-4">
                                {displayField(
                                    'RestrictedVisibility_StopAssessPlan',
                                ) && (
                                    <CheckFieldLabel
                                        id="stopAssessPlan"
                                        checked={
                                            restrictedVisibility.stopAssessPlan
                                        }
                                        onCheckedChange={(checked) => {
                                            setRestrictedVisibility({
                                                ...restrictedVisibility,
                                                stopAssessPlan:
                                                    checked === true,
                                            })
                                        }}
                                        label="Stopped, assessed, planned"
                                        variant="warning"
                                        className="mb-2"
                                    />
                                )}

                                {displayField(
                                    'RestrictedVisibility_CrewBriefing',
                                ) && (
                                    <CheckFieldLabel
                                        id="crewBriefing"
                                        checked={
                                            restrictedVisibility.crewBriefing
                                        }
                                        onCheckedChange={(checked) => {
                                            setRestrictedVisibility({
                                                ...restrictedVisibility,
                                                crewBriefing: checked === true,
                                            })
                                        }}
                                        label="Briefed crew"
                                        variant="warning"
                                        className="mb-2"
                                    />
                                )}

                                {displayField(
                                    'RestrictedVisibility_NavLights',
                                ) && (
                                    <CheckFieldLabel
                                        id="navLights"
                                        checked={restrictedVisibility.navLights}
                                        onCheckedChange={(checked) => {
                                            setRestrictedVisibility({
                                                ...restrictedVisibility,
                                                navLights: checked === true,
                                            })
                                        }}
                                        label="Navigation lights on"
                                        variant="warning"
                                        className="mb-2"
                                    />
                                )}

                                <Separator className="my-4" />

                                {displayField(
                                    'RestrictedVisibility_SoundSignal',
                                ) && (
                                    <div className="pl-4">
                                        <Label
                                            htmlFor="soundSignal"
                                            label="Sounds signals used (pick one)"
                                            className="mb-2"
                                        />
                                        <RadioGroup
                                            defaultValue={
                                                restrictedVisibility.soundSignal
                                            }
                                            onValueChange={(value) => {
                                                setRestrictedVisibility({
                                                    ...restrictedVisibility,
                                                    soundSignal: value,
                                                })
                                            }}
                                            className="space-y-2">
                                            {/* <RadioGroupItem value="option1" />
                                            <RadioGroupItem value="option2" /> */}
                                            <CheckFieldLabel
                                                type="radio"
                                                id="soundSignalNone"
                                                value="None"
                                                label="None needed"
                                                radioGroupValue={
                                                    restrictedVisibility.soundSignal
                                                }
                                                variant="warning"
                                                onCheckedChange={() =>
                                                    setRestrictedVisibility({
                                                        ...restrictedVisibility,
                                                        soundSignal: 'None',
                                                    })
                                                }
                                            />

                                            <CheckFieldLabel
                                                type="radio"
                                                id="soundSignalMakingWay"
                                                value="MakingWay"
                                                label="Making way (1 long / 2 mins)"
                                                radioGroupValue={
                                                    restrictedVisibility.soundSignal
                                                }
                                                variant="warning"
                                                onCheckedChange={() =>
                                                    setRestrictedVisibility({
                                                        ...restrictedVisibility,
                                                        soundSignal:
                                                            'MakingWay',
                                                    })
                                                }
                                            />

                                            <CheckFieldLabel
                                                type="radio"
                                                id="soundSignalNotMakingWay"
                                                value="NotMakingWay"
                                                label="Not making way (2 long / 2 mins)"
                                                radioGroupValue={
                                                    restrictedVisibility.soundSignal
                                                }
                                                variant="warning"
                                                onCheckedChange={() =>
                                                    setRestrictedVisibility({
                                                        ...restrictedVisibility,
                                                        soundSignal:
                                                            'NotMakingWay',
                                                    })
                                                }
                                            />

                                            <CheckFieldLabel
                                                type="radio"
                                                id="soundSignalTowing"
                                                value="Towing"
                                                label="Towing (1 long + 2 short / 2 mins)"
                                                radioGroupValue={
                                                    restrictedVisibility.soundSignal
                                                }
                                                variant="warning"
                                                onCheckedChange={() =>
                                                    setRestrictedVisibility({
                                                        ...restrictedVisibility,
                                                        soundSignal: 'Towing',
                                                    })
                                                }
                                            />
                                        </RadioGroup>
                                    </div>
                                )}

                                <Separator className="my-4" />

                                {displayField(
                                    'RestrictedVisibility_Lookout',
                                ) && (
                                    <CheckFieldLabel
                                        id="lookout"
                                        checked={restrictedVisibility.lookout}
                                        onCheckedChange={(checked) => {
                                            setRestrictedVisibility({
                                                ...restrictedVisibility,
                                                lookout: checked === true,
                                            })
                                        }}
                                        label="Set proper lookout"
                                        variant="warning"
                                        className="mb-2"
                                    />
                                )}

                                {displayField(
                                    'RestrictedVisibility_SoundSignals',
                                ) && (
                                    <CheckFieldLabel
                                        id="soundSignals"
                                        checked={
                                            restrictedVisibility.soundSignals
                                        }
                                        onCheckedChange={(checked) => {
                                            setRestrictedVisibility({
                                                ...restrictedVisibility,
                                                soundSignals: checked === true,
                                            })
                                        }}
                                        label="Listening for other sound signals"
                                        variant="warning"
                                        className="mb-2"
                                    />
                                )}

                                {displayField(
                                    'RestrictedVisibility_RadarWatch',
                                ) && (
                                    <CheckFieldLabel
                                        id="radarWatch"
                                        checked={
                                            restrictedVisibility.radarWatch
                                        }
                                        onCheckedChange={(checked) => {
                                            setRestrictedVisibility({
                                                ...restrictedVisibility,
                                                radarWatch: checked === true,
                                            })
                                        }}
                                        label="Radar watch on"
                                        variant="warning"
                                        className="mb-2"
                                    />
                                )}

                                {displayField(
                                    'RestrictedVisibility_RadioWatch',
                                ) && (
                                    <CheckFieldLabel
                                        id="radioWatch"
                                        checked={
                                            restrictedVisibility.radioWatch
                                        }
                                        onCheckedChange={(checked) => {
                                            setRestrictedVisibility({
                                                ...restrictedVisibility,
                                                radioWatch: checked === true,
                                            })
                                        }}
                                        label="Radio watch on"
                                        variant="warning"
                                        className="mb-2"
                                    />
                                )}
                                <div className="w-full flex flex-col space-y-2">
                                    <CloudFlareCaptures
                                        inputId={selectedEvent?.id || 0}
                                        sectionId={currentTrip.id}
                                        buttonType={'button'}
                                        sectionName={'tripEventID'}
                                    />
                                </div>
                            </div>
                        </div>
                    </SheetBody>
                    <SheetFooter>
                        <Button
                            iconLeft={Check}
                            onClick={() => setOpenProcedureChecklist(false)}>
                            Save
                        </Button>
                    </SheetFooter>
                </SheetContent>
            </Sheet>
            <AlertDialogNew
                openDialog={openRiskDialog}
                setOpenDialog={setOpenRiskDialog}
                handleCreate={handleSaveRisk}
                title={currentRisk?.id > 0 ? 'Update Risk' : 'Create New Risk'}
                actionText={currentRisk?.id > 0 ? 'Update' : 'Create Risk'}>
                <div className="mb-4">
                    <Label htmlFor="impact" label="Risk" />
                    {allRisks && (
                        <Combobox
                            id="impact"
                            options={allRisks}
                            placeholder="Select or enter a risk"
                            value={riskValue}
                            onChange={handleRiskValue}
                            buttonClassName="w-full"
                        />
                    )}
                </div>
                <div className="mb-4">
                    <Label htmlFor="risk-impact" label="Risk impact" />
                    <Combobox
                        id="impact"
                        options={riskImpacts}
                        placeholder="Select risk impact"
                        value={
                            currentRisk?.impact
                                ? riskImpacts?.find(
                                      (impact: any) =>
                                          impact.value == currentRisk?.impact,
                                  )
                                : null
                        }
                        onChange={(value: any) =>
                            setCurrentRisk({
                                ...currentRisk,
                                impact: value?.value,
                            })
                        }
                        buttonClassName="w-full"
                    />
                </div>
                <div className="mb-6">
                    <Label
                        htmlFor="risk-probability"
                        label="Risk probability"
                    />
                    <div className="px-1">
                        <Slider
                            defaultValue={[currentRisk?.probability || 5]}
                            className="my-4"
                            onValueChange={(value: number[]) =>
                                setCurrentRisk({
                                    ...currentRisk,
                                    probability: value[0],
                                })
                            }
                        />
                        <div className="flex justify-between text-xs text-gray-500 mt-1">
                            <span>Low</span>
                            <span>High</span>
                        </div>
                    </div>
                </div>
                <div className="w-full">
                    <H4 className="text-lg font-semibold leading-6 text-gray-700 mb-4">
                        Mitigation strategy
                    </H4>

                    {currentRisk?.mitigationStrategy?.nodes?.length > 0 && (
                        <div className="mb-6 p-4 bg-gray-50 rounded-md">
                            {currentRisk?.mitigationStrategy?.nodes.map(
                                (s: any) => (
                                    <div key={s.id} className="mb-2 last:mb-0">
                                        <div
                                            className="text-sm"
                                            dangerouslySetInnerHTML={{
                                                __html: s.strategy,
                                            }}></div>
                                    </div>
                                ),
                            )}
                        </div>
                    )}

                    {content && (
                        <div className="mb-4 p-4 bg-gray-50 rounded-md">
                            <div
                                className="text-sm"
                                dangerouslySetInnerHTML={{
                                    __html: content,
                                }}></div>
                        </div>
                    )}

                    <div className="flex justify-end mt-4">
                        <Button
                            variant="primary"
                            className="bg-orange-400 hover:bg-orange-500"
                            onClick={() => setOpenRecommendedstrategy(true)}>
                            Add strategy
                        </Button>
                    </div>
                </div>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={openRecommendedstrategy}
                setOpenDialog={setOpenRecommendedstrategy}
                handleCreate={handleNewStrategy}
                title="Recommended strategy"
                actionText="Save">
                <div className="space-y-4 mb-6">
                    <H4>Available strategies</H4>
                    {recommendedStratagies ? (
                        <>
                            <div className="grid grid-cols-1 gap-3">
                                {recommendedStratagies?.map((risk: any) => (
                                    <Button
                                        key={risk.id}
                                        onClick={() => {
                                            handleSetCurrentStrategies(risk)
                                            if (currentRisk) {
                                                handleSetRiskValue({
                                                    title: currentRisk.title,
                                                    mitigationStrategy: {
                                                        nodes: [risk],
                                                    },
                                                })
                                            }
                                        }}
                                        className={`${currentStrategies?.find((s: any) => s.id === risk.id) ? 'border-orange-400 bg-orange-50' : 'border-gray-200 bg-gray-50'} border p-4 rounded-lg cursor-pointer text-left w-full`}>
                                        <div
                                            className="text-sm"
                                            dangerouslySetInnerHTML={{
                                                __html: risk?.strategy,
                                            }}></div>
                                    </Button>
                                ))}
                            </div>
                            <H4 className="text-lg font-normal leading-6 text-gray-700 mt-6">
                                or add new Mitigation strategy
                            </H4>
                        </>
                    ) : (
                        <>
                            <H4 className="p-4 bg-gray-50 rounded-md text-gray-600 text-center">
                                No recommendations available!
                            </H4>
                            <H4 className="text-lg font-normal leading-6 mt-4 mb-2 text-gray-700">
                                Create a new strategy instead
                            </H4>
                        </>
                    )}
                </div>
                <div className="w-full">
                    <Label
                        htmlFor="strategy"
                        label="Strategy details"
                        className="block mb-2 font-medium text-gray-700"
                    />
                    <div className="border rounded-md overflow-hidden">
                        <Editor
                            id="strategy"
                            placeholder="Mitigation strategy"
                            className="w-full"
                            content={content}
                            handleEditorChange={handleEditorChange}
                        />
                    </div>
                </div>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={openDeleteConfirmation}
                setOpenDialog={setOpenDeleteConfirmation}
                handleCreate={handleDeleteRisk}
                title="Delete risk analysis!"
                actionText="Delete"
                showDestructiveAction={true}
                destructiveActionText="Delete"
                handleDestructiveAction={handleDeleteRisk}>
                <H4>Are you sure you want to delete this risk?</H4>
            </AlertDialogNew>
        </div>
    )
}
