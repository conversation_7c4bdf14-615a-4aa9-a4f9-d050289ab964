'use client'
import { usePathname, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { JwtPayload, jwtDecode } from 'jwt-decode'
import { isEmpty } from 'lodash'
import { UserbackProvider } from '@userback/react'
import Loading from '../app/loading'

const USERBACK_TOKEN = 'P-otInIwsjplJMgK8EfvZiYsT3R'

const AuthProvider = ({ children }: { children: React.ReactNode }) => {
    const router = useRouter()
    const pathname = usePathname()
    const [isAuthenticated, setIsAuthenticated] = useState<boolean>()
    const [loading, setLoading] = useState(true)

    useEffect(() => {
        // Paths that don't require authentication
        const exemptedPaths = [
            '/login',
            '/lost-password',
            '/reset-password',
            '/redirect',
        ]

        if (!exemptedPaths.includes(pathname)) {
            // Check if we're in a browser environment
            if (typeof window !== 'undefined') {
                const token = localStorage.getItem('sl-jwt') ?? ''
                if (isEmpty(token)) {
                    console.log(
                        'AuthProvider: Token is empty, redirecting to login',
                    )
                    setIsAuthenticated(false)
                } else {
                    try {
                        const decoded = jwtDecode<JwtPayload>(token)
                        const { exp } = decoded
                        if (Date.now() >= (exp || 1) * 1000) {
                            console.log(
                                'AuthProvider: Token is expired. Removing the token and redirecting to login',
                                Date.now(),
                                exp,
                            )
                            // Clear expired token
                            localStorage.removeItem('sl-jwt')
                            setIsAuthenticated(false)
                        } else {
                            setIsAuthenticated(true)
                        }
                    } catch (error) {
                        console.error(
                            'AuthProvider: Error decoding token',
                            error,
                        )
                        localStorage.removeItem('sl-jwt')
                        setIsAuthenticated(false)
                    }
                }
            } else {
                // Server-side rendering, we'll handle auth on client
                setIsAuthenticated(true)
            }
        } else {
            console.log('AuthProvider: Exempted path', pathname)
            setIsAuthenticated(true)
        }

        setLoading(false)
    }, [pathname])

    // Handle loading state
    if (loading) {
        return <Loading />
    }

    // Handle unauthenticated state
    if (isAuthenticated === false && !pathname.startsWith('/login')) {
        router.push('/login')
        return <Loading />
    }

    return (
        // <UserbackProvider token={USERBACK_TOKEN}>
            <>
            {children}
            </>
            // {/* </UserbackProvider> */}
    )
}

export default AuthProvider
