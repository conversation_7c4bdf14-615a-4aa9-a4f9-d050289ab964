"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"1e361eaecfd8\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NDkzNyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFlMzYxZWFlY2ZkOFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/auth-provider.tsx":
/*!******************************************!*\
  !*** ./src/components/auth-provider.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var jwt_decode__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! jwt-decode */ \"(app-pages-browser)/./node_modules/.pnpm/jwt-decode@4.0.0/node_modules/jwt-decode/build/esm/index.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _app_loading__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../app/loading */ \"(app-pages-browser)/./src/app/loading.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst USERBACK_TOKEN = \"P-otInIwsjplJMgK8EfvZiYsT3R\";\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        // Paths that don't require authentication\n        const exemptedPaths = [\n            \"/login\",\n            \"/lost-password\",\n            \"/reset-password\",\n            \"/redirect\"\n        ];\n        if (!exemptedPaths.includes(pathname)) {\n            // Check if we're in a browser environment\n            if (true) {\n                var _localStorage_getItem;\n                const token = (_localStorage_getItem = localStorage.getItem(\"sl-jwt\")) !== null && _localStorage_getItem !== void 0 ? _localStorage_getItem : \"\";\n                if (lodash_isEmpty__WEBPACK_IMPORTED_MODULE_4___default()(token)) {\n                    console.log(\"AuthProvider: Token is empty, redirecting to login\");\n                    setIsAuthenticated(false);\n                } else {\n                    try {\n                        const decoded = (0,jwt_decode__WEBPACK_IMPORTED_MODULE_3__.jwtDecode)(token);\n                        const { exp } = decoded;\n                        if (Date.now() >= (exp || 1) * 1000) {\n                            console.log(\"AuthProvider: Token is expired. Removing the token and redirecting to login\", Date.now(), exp);\n                            // Clear expired token\n                            localStorage.removeItem(\"sl-jwt\");\n                            setIsAuthenticated(false);\n                        } else {\n                            setIsAuthenticated(true);\n                        }\n                    } catch (error) {\n                        console.error(\"AuthProvider: Error decoding token\", error);\n                        localStorage.removeItem(\"sl-jwt\");\n                        setIsAuthenticated(false);\n                    }\n                }\n            } else {}\n        } else {\n            console.log(\"AuthProvider: Exempted path\", pathname);\n            setIsAuthenticated(true);\n        }\n        setLoading(false);\n    }, [\n        pathname\n    ]);\n    // Handle loading state\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\auth-provider.tsx\",\n            lineNumber: 74,\n            columnNumber: 16\n        }, undefined);\n    }\n    // Handle unauthenticated state\n    if (isAuthenticated === false && !pathname.startsWith(\"/login\")) {\n        router.push(\"/login\");\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_loading__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\components\\\\auth-provider.tsx\",\n            lineNumber: 80,\n            columnNumber: 16\n        }, undefined);\n    }\n    return(// <UserbackProvider token={USERBACK_TOKEN}>\n    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false));\n};\n_s(AuthProvider, \"J8FnL8S13g/at6BDywnuMd4aM8w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname\n    ];\n});\n_c = AuthProvider;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AuthProvider);\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth-provider.tsx\n"));

/***/ })

});