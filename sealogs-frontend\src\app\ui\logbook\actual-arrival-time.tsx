'use client'

import React, { useEffect, useState } from 'react'
import dayjs from 'dayjs'
import { UpdateTripReport_LogBookEntrySection } from '@/app/lib/graphQL/mutation'
import { useMutation } from '@apollo/client'
import { TimePicker } from '@/components/ui/time-picker'
import { useToast } from '@/hooks/use-toast'

import TripReport_LogBookEntrySectionModel from '@/app/offline/models/tripReport_LogBookEntrySection'
import { Label } from '@/components/ui/label'

export default function ActualArrival({
    currentTrip,
    updateTripReport,
    tripReport,
    offline = false,
}: {
    currentTrip: any
    updateTripReport?: any
    tripReport: any
    offline?: boolean
}) {
    const { toast } = useToast()
    const tripReportModel = new TripReport_LogBookEntrySectionModel()
    const [time, setTime] = useState<any>(false)

    const handleTimeChange = async (date: Date) => {
        const arrive = dayjs(date).format('YYYY-MM-DD HH:mm') // Mukul: This is the format that the backend expects
        setTime(dayjs(date).format('HH:mm'))
        if (offline) {
            const data = await tripReportModel.save({
                id: currentTrip.id,
                arrive: arrive,
            })
            updateTripReport({
                id: [...tripReport.map((trip: any) => trip.id), data.id],
                currentTripID: currentTrip.id,
                key: 'arrive',
                value: arrive,
            })
        } else {
            updateTripReport_LogBookEntrySection({
                variables: {
                    input: {
                        id: currentTrip.id,
                        arrive: arrive,
                    },
                },
            })
        }
    }

    const [updateTripReport_LogBookEntrySection] = useMutation(
        UpdateTripReport_LogBookEntrySection,
        {
            onCompleted: (data) => {
                updateTripReport({
                    id: [...tripReport.map((trip: any) => trip.id), data.id],
                    currentTripID: currentTrip.id,
                    key: 'arrive',
                    value: time,
                })
            },
            onError: (error) => {
                console.error('onError', error)
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description: 'Failed to update arrival time',
                })
            },
        },
    )

    useEffect(() => {
        if (tripReport) {
            if (
                time !==
                tripReport?.find((trip: any) => trip.id === currentTrip.id)
                    ?.arrive
                    ? convertTimeFormat(
                          tripReport?.find(
                              (trip: any) => trip.id === currentTrip.id,
                          )?.arrive,
                      )
                    : ''
            ) {
                setTime(
                    tripReport?.find((trip: any) => trip.id === currentTrip.id)
                        ?.arrive
                        ? convertTimeFormat(
                              tripReport?.find(
                                  (trip: any) => trip.id === currentTrip.id,
                              )?.arrive,
                          )
                        : '',
                )
            }
        }
    }, [tripReport])

    const convertTimeFormat = (time: string) => {
        return dayjs(time).format('HH:mm')
    }

    return (
        <Label
            className="w-full"
            label="Actual arrival time"
            htmlFor="actual-arrival-time">
            <TimePicker
                value={
                    time
                        ? dayjs(
                              `${dayjs().format('YYYY-MM-DD')} ${time}`,
                          ).toDate()
                        : tripReport?.find(
                                (trip: any) => trip.id === currentTrip.id,
                            )?.arrive
                          ? dayjs(
                                `${dayjs().format('YYYY-MM-DD')} ${convertTimeFormat(
                                    tripReport?.find(
                                        (trip: any) =>
                                            trip.id === currentTrip.id,
                                    )?.arrive,
                                )}`,
                            ).toDate()
                          : new Date()
                }
                nowButton
                onChange={handleTimeChange}
                use24Hour={true}
                className="max-w-sm"
            />
        </Label>
    )
}
