'use client'
import React, { useEffect, useState } from 'react'
import {
    UpdateBarCrossingChecklist,
    CreateBarCrossingChecklist,
    UpdateEventType_BarCrossing,
    CreateMitigationStrategy,
    UpdateMitigationStrategy,
    CreateRiskFactor,
    UpdateRiskFactor,
} from '@/app/lib/graphQL/mutation'
import {
    BarCrossingChecklist,
    GetRiskFactors,
    CrewMembers_LogBookEntrySection,
} from '@/app/lib/graphQL/query'
import { useLazyQuery, useMutation } from '@apollo/client'
import { useToast } from '@/hooks/use-toast'
import { useSearchParams } from 'next/navigation'
import { getLogBookEntryByID } from '@/app/lib/actions'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import LogBookEntryModel from '@/app/offline/models/logBookEntry'
import BarCrossingChecklistModel from '@/app/offline/models/barCrossingChecklist'
import RiskFactorModel from '@/app/offline/models/riskFactor'
import CrewMembers_LogBookEntrySectionModel from '@/app/offline/models/crewMembers_LogBookEntrySection'
import { generateUniqueId } from '@/app/offline/helpers/functions'
import EventType_BarCrossingModel from '@/app/offline/models/eventType_BarCrossing'
import MitigationStrategyModel from '@/app/offline/models/mitigationStrategy'
import { uniqBy } from 'lodash'

import {
    RiskAnalysisSheet,
    RiskAnalysisContent,
    RiskDialog,
    StrategyDialog,
} from '@/components/ui/risk-analysis'

export default function BarCrossingRiskAnalysis({
    selectedEvent = false,
    onSidebarClose,
    barCrossingChecklistID = 0,
    setBarCrossingChecklistID,
    offline = false,
    setAllChecked,
    crewMembers = false,
    logBookConfig,
    currentTrip,
    open = false,
    onOpenChange,
    noSheet = false,
}: {
    selectedEvent: any
    onSidebarClose: any
    barCrossingChecklistID: number
    setBarCrossingChecklistID: any
    offline?: boolean
    setAllChecked: any
    crewMembers?: any
    logBookConfig?: any
    currentTrip?: any
    open?: boolean
    onOpenChange?: (open: boolean) => void
    noSheet?: boolean
}) {
    const searchParams = useSearchParams()
    const vesselID = searchParams.get('vesselID') ?? 0
    const logentryID = parseInt(searchParams.get('logentryID') ?? '0')
    const { toast } = useToast()
    const [riskAnalysis, setRiskAnalysis] = useState<any>(false)
    const [riskBuffer, setRiskBuffer] = useState<any>({})
    const [openRiskDialog, setOpenRiskDialog] = useState(false)
    const [currentRisk, setCurrentRisk] = useState<any>({})
    const [content, setContent] = useState<any>('')
    const [allRisks, setAllRisks] = useState<any>(false)
    const [allRiskFactors, setAllRiskFactors] = useState<any>([])
    const [riskValue, setRiskValue] = useState<any>(null)
    const [updateStrategy, setUpdateStrategy] = useState(false)
    const [openRecommendedstrategy, setOpenRecommendedstrategy] =
        useState(false)
    const [recommendedStratagies, setRecommendedStratagies] =
        useState<any>(false)
    const [currentStrategies, setCurrentStrategies] = useState<any>([])
    const [recommendedstrategy, setRecommendedstrategy] = useState<any>(false)
    const [riskToDelete, setRiskToDelete] = useState<any>(false)
    const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false)
    const [logbook, setLogbook] = useState<any>(false)
    const [members, setMembers] = useState<any>(false)
    const [creatingBarCrossingChecklist, setCreatingBarCrossingChecklist] =
        useState(false)
    const [permissions, setPermissions] = useState<any>(false)
    const [edit_risks, setEdit_risks] = useState<any>(false)
    const [delete_risks, setDelete_risks] = useState<any>(false)
    const [editBarCrossingRisk, setEditBarCrossingRisk] = useState<any>(false)
    const logBookEntryModel = new LogBookEntryModel()
    const barCrossingChecklistModel = new BarCrossingChecklistModel()
    const riskFactorModel = new RiskFactorModel()
    const crewMemberModel = new CrewMembers_LogBookEntrySectionModel()
    const barCrossingModel = new EventType_BarCrossingModel()
    const mitigationStrategyModel = new MitigationStrategyModel()
    const [selectedAuthor, setSelectedAuthor] = useState<any>(null)

    const init_permissions = () => {
        if (permissions) {
            setEdit_risks(hasPermission('EDIT_RISK', permissions))
            setDelete_risks(hasPermission('DELETE_RISK', permissions))
            setEditBarCrossingRisk(
                hasPermission('EDIT_LOGBOOKENTRY_RISK_ANALYSIS', permissions),
            )
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    const [
        getSectionCrewMembers_LogBookEntrySection,
        { loading: crewMembersLoading },
    ] = useLazyQuery(CrewMembers_LogBookEntrySection, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            let data = response.readCrewMembers_LogBookEntrySections.nodes
            const crewMembers = data
                .map((member: any) => ({
                    label: `${member.crewMember.firstName ?? ''} ${member.crewMember.surname ?? ''}`,
                    value: member.crewMember.id,
                }))
                .filter((member: any) => member.value != logbook.master.id)
            setMembers(uniqBy([...(members || []), ...crewMembers], 'value'))
        },
        onError: (error: any) => {
            console.error('CrewMembers_LogBookEntrySection error', error)
        },
    })

    const handleSetLogbook = async (logbook: any) => {
        setLogbook(logbook)
        const master = {
            label: `${logbook.master.firstName ?? ''} ${logbook.master.surname ?? ''}`,
            value: logbook.master.id,
        }
        if (+master.value > 0) {
            if (Array.isArray(members)) {
                setMembers(uniqBy([...(members || []), master], 'value'))
            } else {
                setMembers([master])
            }
        }

        const sections = logbook.logBookEntrySections.nodes.filter(
            (node: any) =>
                node.className === 'SeaLogs\\CrewMembers_LogBookEntrySection',
        )
        if (sections && sections.length > 0) {
            const sectionIDs = sections.map((section: any) => section.id)
            if (offline) {
                const data = await crewMemberModel.getByIds(sectionIDs)
                const crewMembers = data.map((member: any) => ({
                    label: `${member.crewMember.firstName ?? ''} ${member.crewMember.surname ?? ''}`,
                    value: member.crewMember.id,
                }))
                setMembers(
                    Array.isArray(members)
                        ? [...(members || []), ...crewMembers]
                        : crewMembers,
                )
            } else {
                getSectionCrewMembers_LogBookEntrySection({
                    variables: { filter: { id: { in: sectionIDs } } },
                })
            }
        }
    }

    if (logentryID > 0 && !offline) {
        getLogBookEntryByID(+logentryID, handleSetLogbook)
    }

    useEffect(() => {
        if (crewMembers) {
            const mapped = crewMembers.map((member: any) => ({
                label: `${member.crewMember.firstName ?? ''} ${member.crewMember.surname ?? ''}`,
                value: member.crewMemberID,
            }))
            setMembers(mapped)
        }
    }, [crewMembers])

    const handleBCRAFieldChange = (field: string) => async (check: boolean) => {
        if (!editBarCrossingRisk || !edit_risks) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: 'You do not have permission to edit this section',
            })
            return
        }
        setRiskBuffer({
            ...riskBuffer,
            [field]: check ? 'on' : 'off',
        })
        if (+riskAnalysis?.id > 0) {
            if (offline) {
                await barCrossingChecklistModel.save({
                    id: riskAnalysis.id,
                    [field]: check,
                })
            } else {
                updateBarCrossingChecklist({
                    variables: {
                        input: { id: riskAnalysis.id, [field]: check },
                    },
                })
            }
        }
    }

    const [updateBarCrossingChecklist] = useMutation(
        UpdateBarCrossingChecklist,
        {
            onCompleted: () => {},
            onError: (error) => {
                console.error('onError', error)
            },
        },
    )

    // Fix the fields by explicitly adding a "checked" key.
    const fields = [
        {
            name: 'StopAssessPlan',
            label: 'Stopped, Assessed, Planned',
            value: 'stopAssessPlan',
            checked: riskBuffer?.stopAssessPlan
                ? riskBuffer.stopAssessPlan === 'on'
                : riskAnalysis?.stopAssessPlan,
            handleChange: handleBCRAFieldChange('stopAssessPlan'),
            description: (
                <small>
                    <div>
                        Pause before crossing to evaluate conditions and create
                        a detailed crossing plan.
                    </div>
                </small>
            ),
        },
        {
            name: 'CrewBriefing',
            label: 'Briefed crew on crossing',
            value: 'crewBriefing',
            checked: riskBuffer?.crewBriefing
                ? riskBuffer.crewBriefing === 'on'
                : riskAnalysis?.crewBriefing,
            handleChange: handleBCRAFieldChange('crewBriefing'),
            description: (
                <small>
                    <div>
                        Inform the crew about the crossing plan and any
                        potential hazards.
                    </div>
                </small>
            ),
        },
        {
            name: 'Weather',
            label: 'Weather, tide, bar conditions checked as suitable for crossing',
            value: 'weather',
            checked: riskBuffer?.weather
                ? riskBuffer.weather === 'on'
                : riskAnalysis?.weather,
            handleChange: handleBCRAFieldChange('weather'),
            description: (
                <small>
                    <div>
                        Verify that weather, tide, and bar conditions are
                        favorable and safe for crossing.
                    </div>
                </small>
            ),
        },
        {
            name: 'Stability',
            label: 'Adequate stability checked',
            value: 'stability',
            checked: riskBuffer?.stability
                ? riskBuffer.stability === 'on'
                : riskAnalysis?.stability,
            handleChange: handleBCRAFieldChange('stability'),
            description: (
                <small>
                    <div>
                        Ensure the vessel is stable enough to handle the
                        crossing without capsizing.
                    </div>
                </small>
            ),
        },
        {
            name: 'LifeJackets',
            label: 'Lifejackets on',
            value: 'lifeJackets',
            checked: riskBuffer?.lifeJackets
                ? riskBuffer.lifeJackets === 'on'
                : riskAnalysis?.lifeJackets,
            handleChange: handleBCRAFieldChange('lifeJackets'),
            description: (
                <small>
                    <div>
                        Instruct crew to wear lifejackets if conditions are
                        rough or challenging.
                    </div>
                </small>
            ),
        },
        {
            name: 'WaterTightness',
            label: 'Water tightness checked',
            value: 'waterTightness',
            checked: riskBuffer?.waterTightness
                ? riskBuffer.waterTightness === 'on'
                : riskAnalysis?.waterTightness,
            handleChange: handleBCRAFieldChange('waterTightness'),
            description: (
                <small>
                    <div>
                        Confirm that all hatches and doors are secured to
                        prevent water ingress.
                    </div>
                </small>
            ),
        },
        {
            name: 'LookoutPosted',
            label: 'Lookout posted',
            value: 'lookoutPosted',
            checked: riskBuffer?.lookoutPosted
                ? riskBuffer.lookoutPosted === 'on'
                : riskAnalysis?.lookoutPosted,
            handleChange: handleBCRAFieldChange('lookoutPosted'),
            description: (
                <small>
                    <div>
                        Assign a crew member to watch for hazards or changes in
                        conditions during the crossing.
                    </div>
                </small>
            ),
        },
    ]

    useEffect(() => {
        if (setAllChecked) {
            setAllChecked(fields.every((field) => field.checked))
        }
    }, [fields])

    const offlineGetRiskAnalysis = async () => {
        const data = await barCrossingChecklistModel.getById(
            barCrossingChecklistID > 0
                ? barCrossingChecklistID
                : selectedEvent?.eventType_BarCrossing?.barCrossingChecklistID,
        )
        setRiskAnalysis(data)
    }
    useEffect(() => {
        if (selectedEvent || barCrossingChecklistID > 0) {
            if (
                selectedEvent?.eventType_BarCrossing?.barCrossingChecklist?.id >
                    0 ||
                barCrossingChecklistID > 0
            ) {
                if (offline) {
                    offlineGetRiskAnalysis()
                } else {
                    getRiskAnalysis({
                        variables: {
                            id:
                                barCrossingChecklistID > 0
                                    ? barCrossingChecklistID
                                    : selectedEvent?.eventType_BarCrossing
                                          ?.barCrossingChecklist?.id,
                        },
                    })
                }
            } else {
                if (!creatingBarCrossingChecklist) {
                    setCreatingBarCrossingChecklist(true)
                }
            }
        } else {
            if (!creatingBarCrossingChecklist) {
                setCreatingBarCrossingChecklist(true)
            }
        }
    }, [selectedEvent, barCrossingChecklistID])

    const createOfflineBarCrossingChecklist = async () => {
        const data = await barCrossingChecklistModel.save({
            id: generateUniqueId(),
        })
        setBarCrossingChecklistID(+data.id)
        if (
            barCrossingChecklistID > 0 ||
            selectedEvent?.eventType_BarCrossing?.id
        ) {
            await barCrossingModel.save({
                id:
                    barCrossingChecklistID > 0
                        ? barCrossingChecklistID
                        : selectedEvent?.eventType_BarCrossing?.id,
                barCrossingChecklistID: +data.id,
            })
        }
        const barCrossingChecklistData =
            await barCrossingChecklistModel.getById(data.id)
        setRiskAnalysis(barCrossingChecklistData)
    }
    useEffect(() => {
        if (creatingBarCrossingChecklist) {
            if (offline) {
                createOfflineBarCrossingChecklist()
            } else {
                createBarCrossingChecklist({ variables: { input: {} } })
            }
        }
    }, [creatingBarCrossingChecklist])

    const offlineMount = async () => {
        const data = await riskFactorModel.getByFieldID(
            'type',
            'BarCrossingChecklist',
        )
        const risks = Array.from(
            new Set(data.map((risk: any) => risk.title)),
        ).map((risk: any) => ({ label: risk, value: risk }))
        setAllRisks(risks)
        setAllRiskFactors(data)
    }
    useEffect(() => {
        if (offline) {
            offlineMount()
        } else {
            getRiskFactors({
                variables: { filter: { type: { eq: 'BarCrossingChecklist' } } },
            })
        }
    }, [])

    const [getRiskFactors] = useLazyQuery(GetRiskFactors, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (data) => {
            const risks = Array.from(
                new Set(
                    data.readRiskFactors.nodes?.map((risk: any) => risk.title),
                ),
            ).map((risk: any) => ({ label: risk, value: risk }))
            setAllRisks(risks)
            setAllRiskFactors(data.readRiskFactors.nodes)
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [getRiskAnalysis] = useLazyQuery(BarCrossingChecklist, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (data) => {
            setRiskAnalysis(data.readOneBarCrossingChecklist)
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [createBarCrossingChecklist] = useMutation(
        CreateBarCrossingChecklist,
        {
            onCompleted: (data) => {
                setBarCrossingChecklistID(+data.createBarCrossingChecklist.id)
                if (
                    barCrossingChecklistID > 0 ||
                    selectedEvent?.eventType_BarCrossing?.id
                ) {
                    updateEvent({
                        variables: {
                            input: {
                                id:
                                    barCrossingChecklistID > 0
                                        ? barCrossingChecklistID
                                        : selectedEvent?.eventType_BarCrossing
                                              ?.id,
                                barCrossingChecklistID:
                                    +data.createBarCrossingChecklist.id,
                            },
                        },
                    })
                }
                getRiskAnalysis({
                    variables: { id: data.createBarCrossingChecklist.id },
                })
            },
            onError: (error) => {
                console.error('onError', error)
            },
        },
    )

    const [updateEvent] = useMutation(UpdateEventType_BarCrossing, {
        onCompleted: () => {},
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const updateRiskAnalysisMember = async (memberID: number) => {
        if (!editBarCrossingRisk || !edit_risks) {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: 'You do not have permission to edit this section',
            })
            return
        }
        setRiskBuffer({ ...riskBuffer, memberID })
        if (+riskAnalysis?.id > 0) {
            if (offline) {
                await barCrossingChecklistModel.save({
                    id: riskAnalysis.id,
                    memberID,
                })
            } else {
                updateBarCrossingChecklist({
                    variables: { input: { id: riskAnalysis.id, memberID } },
                })
            }
        }
    }

    const handleEditorChange = (newContent: any) => {
        setContent(newContent)
    }

    const riskImpacts = [
        { value: 'Low', label: 'Low impact' },
        { value: 'Medium', label: 'Medium impact' },
        { value: 'High', label: 'High impact' },
        { value: 'Severe', label: 'Severe impact' },
    ]
    const handleSaveRisk = async () => {
        if (currentRisk.id > 0) {
            if (offline) {
                await riskFactorModel.save({
                    id: currentRisk.id,
                    type: 'BarCrossingChecklist',
                    title: currentRisk.title,
                    impact: currentRisk?.impact || 'Low',
                    probability: currentRisk?.probability || 5,
                    mitigationStrategy:
                        currentStrategies.length > 0
                            ? currentStrategies.map((s: any) => s.id).join(',')
                            : '',
                    barCrossingChecklistID: riskAnalysis?.id,
                })
                setOpenRiskDialog(false)
                const riskFactorData = await riskFactorModel.getByFieldID(
                    'type',
                    'BarCrossingChecklist',
                )
                const risks = Array.from(
                    new Set(riskFactorData.map((risk: any) => risk.title)),
                ).map((risk: any) => ({ label: risk, value: risk }))
                setAllRisks(risks)
                setAllRiskFactors(riskFactorData)
                const barCrossingChecklistData =
                    await barCrossingChecklistModel.getById(
                        barCrossingChecklistID > 0
                            ? barCrossingChecklistID
                            : selectedEvent?.eventType_BarCrossing
                                  ?.barCrossingChecklist?.id,
                    )
                setRiskAnalysis(barCrossingChecklistData)
            } else {
                updateRiskFactor({
                    variables: {
                        input: {
                            id: currentRisk.id,
                            type: 'BarCrossingChecklist',
                            title: currentRisk.title,
                            impact: currentRisk?.impact || 'Low',
                            probability: currentRisk?.probability || 5,
                            mitigationStrategy:
                                currentStrategies.length > 0
                                    ? currentStrategies
                                          .map((s: any) => s.id)
                                          .join(',')
                                    : '',
                            barCrossingChecklistID: riskAnalysis?.id,
                        },
                    },
                })
            }
        } else {
            if (offline) {
                await riskFactorModel.save({
                    id: generateUniqueId(),
                    type: 'BarCrossingChecklist',
                    title: currentRisk.title,
                    impact: currentRisk?.impact || 'Low',
                    probability: currentRisk?.probability || 5,
                    mitigationStrategy:
                        currentStrategies.length > 0
                            ? currentStrategies.map((s: any) => s.id).join(',')
                            : '',
                    barCrossingChecklistID: riskAnalysis?.id,
                    vesselID,
                })

                setOpenRiskDialog(false)
                const riskFactorData = await riskFactorModel.getByFieldID(
                    'type',
                    'BarCrossingChecklist',
                )
                const risks = Array.from(
                    new Set(riskFactorData.map((risk: any) => risk.title)),
                ).map((risk: any) => ({ label: risk, value: risk }))
                setAllRisks(risks)
                setAllRiskFactors(riskFactorData)
                const barCrossingChecklistData =
                    await barCrossingChecklistModel.getById(
                        barCrossingChecklistID > 0
                            ? barCrossingChecklistID
                            : riskAnalysis?.id,
                    )
                setRiskAnalysis(barCrossingChecklistData)
            } else {
                createRiskFactor({
                    variables: {
                        input: {
                            type: 'BarCrossingChecklist',
                            title: currentRisk.title,
                            impact: currentRisk?.impact || 'Low',
                            probability: currentRisk?.probability || 5,
                            mitigationStrategy:
                                currentStrategies.length > 0
                                    ? currentStrategies
                                          .map((s: any) => s.id)
                                          .join(',')
                                    : '',
                            barCrossingChecklistID: riskAnalysis?.id,
                            vesselID,
                        },
                    },
                })
            }
        }
    }

    const [createMitigationStrategy] = useMutation(CreateMitigationStrategy, {
        onCompleted: (data) => {
            setCurrentStrategies([
                ...currentStrategies,
                { id: data.createMitigationStrategy.id, strategy: content },
            ])
            setContent('')
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [updateMitigationStrategy] = useMutation(UpdateMitigationStrategy, {
        onCompleted: () => {},
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [createRiskFactor] = useMutation(CreateRiskFactor, {
        onCompleted: (data) => {
            setOpenRiskDialog(false)
            getRiskFactors({
                variables: { filter: { type: { eq: 'BarCrossingChecklist' } } },
            })
            getRiskAnalysis({
                variables: {
                    id:
                        barCrossingChecklistID > 0
                            ? barCrossingChecklistID
                            : selectedEvent?.eventType_BarCrossing
                                  ?.barCrossingChecklist?.id,
                },
            })
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const [updateRiskFactor] = useMutation(UpdateRiskFactor, {
        onCompleted: () => {
            setOpenRiskDialog(false)
            getRiskFactors({
                variables: { filter: { type: { eq: 'BarCrossingChecklist' } } },
            })
            getRiskAnalysis({
                variables: {
                    id:
                        barCrossingChecklistID > 0
                            ? barCrossingChecklistID
                            : selectedEvent?.eventType_BarCrossing
                                  ?.barCrossingChecklist?.id,
                },
            })
        },
        onError: (error) => {
            console.error('onError', error)
        },
    })

    const handleRiskValue = (v: any) => {
        setCurrentRisk({ ...currentRisk, title: v?.value })
        setRiskValue({ value: v.value, label: v.value })
        if (
            allRiskFactors?.filter(
                (risk: any) =>
                    risk.title === v.value &&
                    risk.mitigationStrategy.nodes?.length > 0,
            ).length > 0
        ) {
            const items = Array.from(
                new Set(
                    allRiskFactors
                        ?.filter(
                            (r: any) =>
                                r.title === v.value &&
                                r.mitigationStrategy.nodes?.length > 0,
                        )
                        .map((r: any) => r.mitigationStrategy.nodes)[0]
                        .map((s: any) => ({
                            id: s.id,
                            strategy: s.strategy,
                        })),
                ),
            )
            setRecommendedStratagies(items)
        } else {
            setRecommendedStratagies(false)
        }
    }

    const handleCreateRisk = (inputValue: any) => {
        setCurrentRisk({ ...currentRisk, title: inputValue })
        setRiskValue({ value: inputValue, label: inputValue })
        if (allRisks) {
            setAllRisks([...allRisks, { value: inputValue, label: inputValue }])
        } else {
            setAllRisks([{ value: inputValue, label: inputValue }])
        }
    }

    const handleDeleteRisk = async () => {
        if (offline) {
            await riskFactorModel.save({
                id: riskToDelete.id,
                barCrossingChecklistID: 0,
                vesselID: 0,
            })
            setOpenRiskDialog(false)
            const riskFactorData = await riskFactorModel.getByFieldID(
                'type',
                'BarCrossingChecklist',
            )
            const risks = Array.from(
                new Set(riskFactorData.map((risk: any) => risk.title)),
            ).map((risk: any) => ({ label: risk, value: risk }))
            setAllRisks(risks)
            setAllRiskFactors(riskFactorData)
            const barCrossingChecklistData =
                await barCrossingChecklistModel.getById(
                    barCrossingChecklistID > 0
                        ? barCrossingChecklistID
                        : selectedEvent?.eventType_BarCrossing
                              ?.barCrossingChecklist?.id,
                )
            setRiskAnalysis(barCrossingChecklistData)
        } else {
            updateRiskFactor({
                variables: {
                    input: {
                        id: riskToDelete.id,
                        barCrossingChecklistID: 0,
                        vesselID: 0,
                    },
                },
            })
        }
        setOpenDeleteConfirmation(false)
    }

    const handleSetCurrentStrategies = (strategy: any) => {
        if (currentStrategies.find((s: any) => s.id === strategy.id)) {
            setCurrentStrategies(
                currentStrategies.filter((s: any) => s.id !== strategy.id),
            )
        } else {
            setCurrentStrategies([...currentStrategies, strategy])
        }
    }

    const handleNewStrategy = async () => {
        if (content) {
            if (offline) {
                const data = await mitigationStrategyModel.save({
                    id: generateUniqueId(),
                    strategy: content,
                })
                const newStrategies = [
                    ...currentStrategies,
                    { id: data.id, strategy: content },
                ]
                setCurrentRisk({
                    ...currentRisk,
                    mitigationStrategy: { nodes: newStrategies },
                })
                setCurrentStrategies(newStrategies)
                setContent('')
            } else {
                createMitigationStrategy({
                    variables: { input: { strategy: content } },
                })
            }
        }
        setOpenRecommendedstrategy(false)
    }

    const handleSetRiskValue = (v: any) => {
        setRiskValue({ value: v.title, label: v.title })
        if (v.mitigationStrategy.nodes) {
            setCurrentStrategies(v.mitigationStrategy.nodes)
        }
        if (
            allRiskFactors?.filter(
                (risk: any) =>
                    risk.title === v.title &&
                    risk.mitigationStrategy.nodes?.length > 0,
            ).length > 0
        ) {
            setRecommendedStratagies(
                Array.from(
                    new Set(
                        allRiskFactors
                            ?.filter(
                                (r: any) =>
                                    r.title === v.title &&
                                    r.mitigationStrategy.nodes?.length > 0,
                            )
                            .map((r: any) => r.mitigationStrategy.nodes)[0]
                            .map((s: any) => ({
                                id: s.id,
                                strategy: s.strategy,
                            })),
                    ),
                ),
            )
        } else {
            setRecommendedStratagies(false)
        }
    }

    const offlineUseEffect = async () => {
        const logbook = await logBookEntryModel.getById(logentryID)
        handleSetLogbook(logbook)
    }
    useEffect(() => {
        if (offline) {
            offlineUseEffect()
        }
    }, [offline])

    useEffect(() => {
        if (members && riskAnalysis) {
            const member = members.find(
                (member: any) => member.value == riskAnalysis.member.id,
            )
            setSelectedAuthor(member)
        }
    }, [members, riskAnalysis])

    const riskAnalysisProps = {
        checkFields: fields,
        riskFactors: riskAnalysis?.riskFactors?.nodes || [],
        crewMembers: members
            ? members.map((m: any) => ({
                  ...m,
                  value: String(m.value),
              }))
            : [],
        selectedAuthor,
        onAuthorChange: (value: any) => {
            setSelectedAuthor(value)
            if (value) {
                updateRiskAnalysisMember(value.value)
            }
        },
        canEdit: editBarCrossingRisk && edit_risks,
        canDeleteRisks: editBarCrossingRisk && delete_risks,
        onRiskClick: (risk: any) => {
            if (!editBarCrossingRisk || !edit_risks) {
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description:
                        'You do not have permission to edit this section',
                })
                return
            }
            handleSetRiskValue(risk)
            setCurrentRisk(risk)
            setOpenRiskDialog(true)
        },
        onAddRiskClick: () => {
            if (!editBarCrossingRisk || !edit_risks) {
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description:
                        'You do not have permission to edit this section',
                })
                return
            }
            setCurrentRisk({})
            setContent('')
            setRiskValue(null)
            setOpenRiskDialog(true)
        },
        onRiskDelete: (risk: any) => {
            if (!editBarCrossingRisk || !delete_risks) {
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description: 'You do not have permission to delete risks',
                })
                return
            }
            setRiskToDelete(risk)
            handleDeleteRisk()
        },
        setAllChecked,
        selectedEvent,
        currentTrip,
    }

    return (
        <div>
            {noSheet ? (
                <RiskAnalysisContent {...riskAnalysisProps} />
            ) : (
                <RiskAnalysisSheet
                    open={open}
                    onOpenChange={(isOpen) => {
                        if (onOpenChange) {
                            onOpenChange(isOpen)
                        }
                    }}
                    onSidebarClose={() => {
                        onSidebarClose()
                        if (onOpenChange) {
                            onOpenChange(false)
                        }
                    }}
                    title="Risk Analysis"
                    subtitle="Bar Crossing"
                    {...riskAnalysisProps}
                />
            )}
            <RiskDialog
                open={openRiskDialog}
                onOpenChange={setOpenRiskDialog}
                currentRisk={currentRisk}
                onSave={handleSaveRisk}
                riskOptions={allRisks}
                riskValue={riskValue}
                onRiskValueChange={handleRiskValue}
                riskImpacts={riskImpacts}
                onRiskImpactChange={(value: any) =>
                    setCurrentRisk({
                        ...currentRisk,
                        impact: value?.value,
                    })
                }
                onRiskProbabilityChange={(value: number) =>
                    setCurrentRisk({
                        ...currentRisk,
                        probability: value,
                    })
                }
                currentStrategies={currentStrategies}
                content={content}
                onAddStrategyClick={() => setOpenRecommendedstrategy(true)}
            />
            <StrategyDialog
                open={openRecommendedstrategy}
                onOpenChange={setOpenRecommendedstrategy}
                onSave={handleNewStrategy}
                currentRisk={currentRisk}
                recommendedStrategies={recommendedStratagies}
                currentStrategies={currentStrategies}
                onStrategySelect={(strategy: any) => {
                    setRecommendedstrategy(strategy)
                    handleSetCurrentStrategies(strategy)
                    setCurrentRisk({
                        ...currentRisk,
                        mitigationStrategy: strategy,
                    })
                    setUpdateStrategy(false)
                }}
                content={content}
                onEditorChange={handleEditorChange}
            />
        </div>
    )
}
