"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/log-entries/page",{

/***/ "(app-pages-browser)/./src/app/ui/logbook/actual-arrival-time.tsx":
/*!****************************************************!*\
  !*** ./src/app/ui/logbook/actual-arrival-time.tsx ***!
  \****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ActualArrival; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._0efb0e13e866a92344c87a16d2520da4/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/graphQL/mutation */ \"(app-pages-browser)/./src/app/lib/graphQL/mutation/index.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useMutation.js\");\n/* harmony import */ var _components_ui_time_picker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/time-picker */ \"(app-pages-browser)/./src/components/ui/time-picker.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/offline/models/tripReport_LogBookEntrySection */ \"(app-pages-browser)/./src/app/offline/models/tripReport_LogBookEntrySection.js\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ActualArrival(param) {\n    let { currentTrip, updateTripReport, tripReport, offline = false } = param;\n    var _tripReport_find, _tripReport_find1;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const tripReportModel = new _app_offline_models_tripReport_LogBookEntrySection__WEBPACK_IMPORTED_MODULE_6__[\"default\"]();\n    const [time, setTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleTimeChange = async (date)=>{\n        const arrive = dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).format(\"YYYY-MM-DD HH:mm\") // Mukul: This is the format that the backend expects\n        ;\n        setTime(dayjs__WEBPACK_IMPORTED_MODULE_2___default()(date).format(\"HH:mm\"));\n        if (offline) {\n            const data = await tripReportModel.save({\n                id: currentTrip.id,\n                arrive: arrive\n            });\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    data.id\n                ],\n                currentTripID: currentTrip.id,\n                key: \"arrive\",\n                value: arrive\n            });\n        } else {\n            updateTripReport_LogBookEntrySection({\n                variables: {\n                    input: {\n                        id: currentTrip.id,\n                        arrive: arrive\n                    }\n                }\n            });\n        }\n    };\n    const [updateTripReport_LogBookEntrySection] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_8__.useMutation)(_app_lib_graphQL_mutation__WEBPACK_IMPORTED_MODULE_3__.UpdateTripReport_LogBookEntrySection, {\n        onCompleted: (data)=>{\n            updateTripReport({\n                id: [\n                    ...tripReport.map((trip)=>trip.id),\n                    data.id\n                ],\n                currentTripID: currentTrip.id,\n                key: \"arrive\",\n                value: time\n            });\n        },\n        onError: (error)=>{\n            console.error(\"onError\", error);\n            toast({\n                variant: \"destructive\",\n                title: \"Error\",\n                description: \"Failed to update arrival time\"\n            });\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (tripReport) {\n            var _tripReport_find, _tripReport_find1;\n            if (time !== (tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find === void 0 ? void 0 : _tripReport_find.arrive) ? convertTimeFormat(tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find1 = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find1 === void 0 ? void 0 : _tripReport_find1.arrive) : \"\") {\n                var _tripReport_find2, _tripReport_find3;\n                setTime((tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find2 = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find2 === void 0 ? void 0 : _tripReport_find2.arrive) ? convertTimeFormat(tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find3 = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find3 === void 0 ? void 0 : _tripReport_find3.arrive) : \"\");\n            }\n        }\n    }, [\n        tripReport\n    ]);\n    const convertTimeFormat = (time)=>{\n        return dayjs__WEBPACK_IMPORTED_MODULE_2___default()(time).format(\"HH:mm\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_7__.Label, {\n        className: \"w-full\",\n        label: \"Actual arrival time\",\n        htmlFor: \"actual-arrival-time\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_time_picker__WEBPACK_IMPORTED_MODULE_4__.TimePicker, {\n            value: time ? dayjs__WEBPACK_IMPORTED_MODULE_2___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"), \" \").concat(time)).toDate() : (tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find === void 0 ? void 0 : _tripReport_find.arrive) ? dayjs__WEBPACK_IMPORTED_MODULE_2___default()(\"\".concat(dayjs__WEBPACK_IMPORTED_MODULE_2___default()().format(\"YYYY-MM-DD\"), \" \").concat(convertTimeFormat(tripReport === null || tripReport === void 0 ? void 0 : (_tripReport_find1 = tripReport.find((trip)=>trip.id === currentTrip.id)) === null || _tripReport_find1 === void 0 ? void 0 : _tripReport_find1.arrive))).toDate() : new Date(),\n            nowButton: true,\n            onChange: handleTimeChange,\n            use24Hour: true,\n            className: \"max-w-sm\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\actual-arrival-time.tsx\",\n            lineNumber: 112,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\logbook\\\\actual-arrival-time.tsx\",\n        lineNumber: 108,\n        columnNumber: 9\n    }, this);\n}\n_s(ActualArrival, \"gZwbjmwxniMX7GVLdsxx91puErU=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_8__.useMutation\n    ];\n});\n_c = ActualArrival;\nvar _c;\n$RefreshReg$(_c, \"ActualArrival\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/logbook/actual-arrival-time.tsx\n"));

/***/ })

});